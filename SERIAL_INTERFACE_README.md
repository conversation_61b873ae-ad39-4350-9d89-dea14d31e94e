# ESPSomfy-RTS Serial Interface

This is a modified version of ESPSomfy-RTS that removes all WiFi/web functionality and provides a serial-only interface for controlling Somfy RTS motorized shades.

## Features

- **Serial-only operation** - No WiFi, web interface, or MQTT
- **Shade control** - Move shades up, down, to MY position, or specific positions
- **Remote pairing** - Pair Somfy remotes to shades via serial commands
- **Remote discovery** - Listen for and discover unknown Somfy remotes
- **Position tracking** - Track shade positions and movement
- **Multiple shade support** - Control up to 32 individual shades

## Hardware Requirements

- ESP32 microcontroller
- CC1101 radio transceiver module (433MHz)
- Serial connection (USB or UART)

## Getting Started

1. **Upload the firmware** to your ESP32
2. **Connect via serial** at 115200 baud
3. **Type `help`** to see available commands

## Basic Commands

### System Commands
- `help` - Show all available commands
- `status` - Show system status and configuration
- `list shades` - List all configured shades
- `info <id>` - Show detailed information for a shade

### Shade Management
- `add shade <name>` - Add a new shade
- `delete <id>` - Delete a shade
- `set <id> <property> <value>` - Set shade properties

### Shade Control
- `<id> up` - Move shade up
- `<id> down` - Move shade down  
- `<id> my` - Move to MY position
- `<id> stop` - Stop movement
- `<id> pos <0-100>` - Move to specific position (%)
- `<id> setmy` - Set current position as MY position

### Remote Management
- `pair <id>` - Enter pairing mode for a shade
- `unpair <id>` - Unpair a shade from its remote
- `listen` - Listen for all remote commands
- `scan` - Scan for unknown remotes
- `test <id>` - Test communication with a shade

## Usage Examples

### Adding and Pairing a Shade

```
> add shade "Living Room Blind"
Added shade 1: Living Room Blind

> pair 1
Pairing mode for shade 1 (Living Room Blind)
Instructions:
1. Press and hold the PROG button on your Somfy remote for 3 seconds
2. The shade should jog to confirm pairing
3. Wait for confirmation message
4. Press any key to exit pairing mode

Listening for PROG commands...
REMOTE: 0x12345678 | Code:   123 | Cmd: Prog     | RSSI: -45 dBm
SUCCESS: Shade 1 paired to remote 0x12345678
```

### Controlling Shades

```
> 1 up
Moving shade 1 UP

> 1 pos 50
Moving shade 1 to position 50.0%

> 1 my
Moving shade 1 to MY position
```

### Discovering Unknown Remotes

```
> scan
Scanning for unknown Somfy remotes...
Press buttons on your Somfy remotes to discover their addresses.
*** NEW REMOTE DISCOVERED: 0x87654321 ***
UNKNOWN: 0x87654321 | Code:   456 | Cmd: Up       | RSSI: -38 dBm | Count: 1
UNKNOWN: 0x87654321 | Code:   457 | Cmd: Down     | RSSI: -38 dBm | Count: 2
```

## Shade Properties

You can configure various shade properties using the `set` command:

- `name` - Shade name
- `uptime` - Time to fully open (milliseconds)
- `downtime` - Time to fully close (milliseconds)  
- `tilttime` - Time for full tilt operation (milliseconds)

Example:
```
> set 1 name "Kitchen Blind"
> set 1 uptime 15000
> set 1 downtime 18000
```

## Remote Commands

The system recognizes these Somfy RTS commands:
- `Up` - Move up/open
- `Down` - Move down/close
- `My` - Move to MY position / Stop
- `Prog` - Programming command (used for pairing)

## Troubleshooting

### Shade Not Responding
1. Check pairing: `info <id>` to verify remote address
2. Test communication: `test <id>`
3. Check transceiver: `status` to verify configuration

### Remote Not Detected
1. Use `listen` mode to see all remote activity
2. Check frequency setting (should be 433.92 MHz)
3. Verify CC1101 wiring and power

### Pairing Issues
1. Ensure shade is in programming mode
2. Hold PROG button for full 3 seconds
3. Shade should jog to confirm pairing
4. Try moving closer to the transceiver

## Technical Notes

- Rolling codes are automatically managed
- Shade positions are tracked in memory
- Configuration is saved to ESP32 flash storage
- Serial interface runs at 115200 baud, 8N1

## Future Enhancements

This serial interface provides the foundation for:
- Raspberry Pi Pico port
- Custom automation scripts
- Integration with other home automation systems
- Standalone operation without WiFi dependency
