/*
 * Simple Somfy RTS Controller
 * 
 * A clean, simplified serial interface for controlling Somfy RTS shades
 * without WiFi/MQTT dependencies. Perfect for Raspberry Pi integration.
 * 
 * Hardware Requirements:
 * - ESP32 development board
 * - CC1101 transceiver module
 * 
 * Default Wiring:
 * CC1101    ESP32
 * VCC   ->  3.3V
 * GND   ->  GND
 * MOSI  ->  GPIO23
 * MISO  ->  GPIO19
 * SCK   ->  GPIO18
 * CSN   ->  GPIO5
 * GDO0  ->  GPIO14
 * GDO2  ->  GPIO12
 * 
 * Serial Interface:
 * - Baud Rate: 115200
 * - Format: JSON and text commands
 * - Real-time status updates
 * 
 * Author: AI Assistant
 * Version: 1.0
 * License: MIT
 */

#include <Arduino.h>
#include <esp_task_wdt.h>
#include <ArduinoJson.h>
#include <ELECHOUSE_CC1101_SRC_DRV.h>
#include <Preferences.h>

// Configuration constants
#define MAX_SHADES 16
#define SYMBOL_DURATION 640  // microseconds
#define FRAME_REPEAT_DELAY 30000  // microseconds between frame repeats

// Somfy RTS Commands
enum class SomfyCommand : uint8_t {
  MY = 0x1,
  UP = 0x2,
  MY_UP = 0x3,
  DOWN = 0x4,
  MY_DOWN = 0x5,
  UP_DOWN = 0x6,
  PROG = 0x8,
  STOP = 0x1  // Alias for MY
};

// Shade movement directions
enum class Direction : int8_t {
  STOPPED = 0,
  UP = 1,
  DOWN = -1
};

// Somfy RTS Frame structure
struct SomfyFrame {
  uint8_t key;
  uint8_t ctrl;
  uint16_t rollingCode;
  uint32_t address;
  uint8_t data[7];
  uint8_t checksum;
  bool valid;
  
  SomfyFrame() : key(0xA7), ctrl(0), rollingCode(0), address(0), checksum(0), valid(false) {
    memset(data, 0, sizeof(data));
  }
  
  void calculateChecksum() {
    checksum = 0;
    checksum ^= key;
    checksum ^= ctrl;
    checksum ^= (rollingCode >> 8) & 0xFF;
    checksum ^= rollingCode & 0xFF;
    checksum ^= (address >> 16) & 0xFF;
    checksum ^= (address >> 8) & 0xFF;
    checksum ^= address & 0xFF;
    for (int i = 0; i < 7; i++) {
      checksum ^= data[i];
    }
  }
  
  void encode(uint8_t* buffer) {
    calculateChecksum();
    
    buffer[0] = key;
    buffer[1] = ctrl;
    buffer[2] = checksum;
    buffer[3] = (rollingCode >> 8) & 0xFF;
    buffer[4] = rollingCode & 0xFF;
    buffer[5] = (address >> 16) & 0xFF;
    buffer[6] = (address >> 8) & 0xFF;
    buffer[7] = address & 0xFF;
    
    // Copy data bytes
    for (int i = 0; i < 7; i++) {
      buffer[8 + i] = data[i];
    }
  }
};

// Individual shade configuration and state
class SomfyShade {
private:
  uint8_t id;
  uint32_t address;
  uint16_t rollingCode;
  char name[32];
  float currentPosition;
  float targetPosition;
  Direction direction;
  uint32_t moveStartTime;
  uint32_t moveDuration;  // milliseconds for full travel
  bool enabled;
  
public:
  SomfyShade() {
    clear();
  }
  
  void clear() {
    id = 255;
    address = 0;
    rollingCode = 1;
    memset(name, 0, sizeof(name));
    currentPosition = 0.0f;
    targetPosition = 0.0f;
    direction = Direction::STOPPED;
    moveStartTime = 0;
    moveDuration = 20000;  // 20 seconds default
    enabled = false;
  }
  
  // Configuration
  void setId(uint8_t shadeId) { id = shadeId; }
  void setAddress(uint32_t addr) { address = addr; }
  void setName(const char* shadeName) {
    if (shadeName) {
      strncpy(name, shadeName, sizeof(name) - 1);
      name[sizeof(name) - 1] = '\0';
    }
  }
  void setMoveDuration(uint32_t duration) { moveDuration = duration; }
  void enable(bool state = true) { enabled = state; }
  
  // Getters
  uint8_t getId() const { return id; }
  uint32_t getAddress() const { return address; }
  const char* getName() const { return name; }
  float getCurrentPosition() const { return currentPosition; }
  float getTargetPosition() const { return targetPosition; }
  Direction getDirection() const { return direction; }
  bool isEnabled() const { return enabled; }
  bool isMoving() const { return direction != Direction::STOPPED; }
  
  // Movement control
  void moveToPosition(float position);
  void moveUp() { moveToPosition(100.0f); }
  void moveDown() { moveToPosition(0.0f); }
  void stop();
  void setMyPosition();
  
  // State management
  void update();  // Call regularly to update position during movement
  void setCurrentPosition(float position) {
    if (position < 0.0f) position = 0.0f;
    if (position > 100.0f) position = 100.0f;
    currentPosition = position;
  }
  
  // Persistence
  void save();
  void load();
  
  // Frame generation
  SomfyFrame createFrame(SomfyCommand command);
  uint16_t getNextRollingCode() {
    rollingCode++;
    if (rollingCode == 0) rollingCode = 1;  // Never use 0
    return rollingCode;
  }
};

// CC1101 transceiver management
class SomfyTransceiver {
private:
  bool initialized;
  float frequency;
  int8_t txPower;
  
public:
  SomfyTransceiver() {
    initialized = false;
    frequency = 433.92f;
    txPower = 10;
  }
  
  void setFrequency(float freq) { frequency = freq; }
  void setTxPower(int8_t power) { txPower = power; }
  bool isInitialized() const { return initialized; }
  
  bool begin();
  void end();
  void sendFrame(const SomfyFrame& frame, uint8_t repeats = 3);
  void sendRawData(uint8_t* data, uint8_t length);
  
private:
  void initializeCC1101();
  void sendWakeup();
  void sendSync();
  void sendSymbol(bool bit);
};

// Main controller class
class SimpleSomfyController {
private:
  SomfyShade shades[MAX_SHADES];
  SomfyTransceiver transceiver;
  Preferences preferences;
  uint32_t baseAddress;
  
public:
  SimpleSomfyController() {
    baseAddress = 0x120000;  // Default base address
  }
  
  // Initialization
  bool begin();
  void end();
  
  // Shade management
  SomfyShade* addShade(uint32_t address, const char* name = nullptr);
  SomfyShade* getShade(uint8_t id);
  SomfyShade* getShadeByAddress(uint32_t address);
  bool removeShade(uint8_t id);
  uint8_t getShadeCount();
  void listShades(JsonDocument& doc);
  
  // Control operations
  bool moveShade(uint8_t id, float position);
  bool stopShade(uint8_t id);
  bool setShadeMyPosition(uint8_t id);
  
  // System operations
  void update();  // Call regularly in main loop
  void saveConfiguration();
  void loadConfiguration();
  void factoryReset();
  
  // Transceiver access
  SomfyTransceiver& getTransceiver() { return transceiver; }
  
  // Status
  void getSystemStatus(JsonDocument& doc);
  void getShadeStatus(uint8_t id, JsonDocument& doc);
  
private:
  uint8_t findFreeSlot();
};

// Command structure for parsing
struct Command {
  String action;
  uint8_t shadeId;
  float position;
  uint32_t address;
  String name;
  bool valid;
  
  Command() : shadeId(255), position(-1), address(0), valid(false) {}
};

// Serial interface class
class SimpleSerialInterface {
private:
  String inputBuffer;
  bool commandReady;
  unsigned long lastHeartbeat;
  
public:
  SimpleSerialInterface() {
    commandReady = false;
    lastHeartbeat = 0;
  }
  
  void begin(unsigned long baudRate = 115200);
  void loop();
  void processCommand(const String& input);
  Command parseCommand(const String& input);
  Command parseTextCommand(const String& input);
  Command parseJsonCommand(const String& input);
  void sendError(const String& message);
  void sendSuccess(const String& message);
  void sendJson(JsonDocument& doc);
  void printWelcome();
  void printHelp();
  void notifyShadeStateChanged(uint8_t shadeId);
  void sendHeartbeat();
  
  // Command handlers
  void handleListCommand();
  void handleSystemCommand();
  void handleMoveCommand(const Command& cmd);
  void handleStopCommand(const Command& cmd);
  void handleAddCommand(const Command& cmd);
  void handleRemoveCommand(const Command& cmd);
  void handleSetNameCommand(const Command& cmd);
  void handleSetMyCommand(const Command& cmd);
  void handleHelpCommand();
  void handleResetCommand();
};

// Global instances
SimpleSomfyController somfyController;
SimpleSerialInterface serialInterface;

// Configuration
const unsigned long SERIAL_BAUD_RATE = 115200;
const unsigned long WATCHDOG_TIMEOUT = 10000;  // 10 seconds
const unsigned long LOOP_DELAY = 10;           // 10ms loop delay

// State tracking for notifications
float lastPositions[MAX_SHADES];
Direction lastDirections[MAX_SHADES];

void setup() {
  // Initialize watchdog timer
  esp_task_wdt_config_t wdt_config = {
    .timeout_ms = WATCHDOG_TIMEOUT,
    .idle_core_mask = (1 << portNUM_PROCESSORS) - 1,
    .trigger_panic = false  // Don't panic, just reset
  };
  esp_task_wdt_init(&wdt_config);
  esp_task_wdt_add(NULL);
  
  // Initialize serial interface first
  serialInterface.begin(SERIAL_BAUD_RATE);
  
  // Initialize Somfy controller
  Serial.println("Initializing Somfy RTS controller...");
  
  if (!somfyController.begin()) {
    Serial.println("ERROR: Failed to initialize Somfy controller!");
    Serial.println("Please check:");
    Serial.println("- CC1101 module wiring");
    Serial.println("- Power supply (3.3V)");
    Serial.println("- SPI connections");
    
    while (true) {
      delay(1000);
      esp_task_wdt_reset();
    }
  }
  
  Serial.println("Somfy RTS controller initialized successfully!");
  Serial.println("CC1101 transceiver ready on 433.92 MHz");
  Serial.println();
  
  // Initialize state tracking
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    lastPositions[i] = -1;
    lastDirections[i] = Direction::STOPPED;
  }
  
  // Load existing configuration
  Serial.print("Loaded ");
  Serial.print(somfyController.getShadeCount());
  Serial.println(" shades from configuration");
  
  Serial.println("Ready for commands!");
  Serial.print("> ");
}

void loop() {
  // Reset watchdog timer
  esp_task_wdt_reset();
  
  // Process serial interface
  serialInterface.loop();
  
  // Update Somfy controller (handles shade movements)
  somfyController.update();
  
  // Check for state changes and send notifications
  checkForStateChanges();
  
  // Small delay to prevent excessive CPU usage
  delay(LOOP_DELAY);
}

void checkForStateChanges() {
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    SomfyShade* shade = somfyController.getShade(i);
    if (!shade) continue;

    float currentPos = shade->getCurrentPosition();
    Direction currentDir = shade->getDirection();

    // Check if position or direction changed significantly
    bool positionChanged = abs(currentPos - lastPositions[i]) > 1.0f;
    bool directionChanged = currentDir != lastDirections[i];

    if (positionChanged || directionChanged) {
      // Send state change notification
      serialInterface.notifyShadeStateChanged(i);

      // Update tracking
      lastPositions[i] = currentPos;
      lastDirections[i] = currentDir;
    }
  }
}

// ============================================================================
// IMPLEMENTATION
// ============================================================================

// SomfyShade implementation
void SomfyShade::moveToPosition(float position) {
  if (!enabled) return;

  // Clamp position to valid range
  if (position < 0.0f) position = 0.0f;
  if (position > 100.0f) position = 100.0f;

  targetPosition = position;

  if (abs(targetPosition - currentPosition) < 1.0f) {
    // Already at target
    direction = Direction::STOPPED;
    return;
  }

  // Determine direction
  if (targetPosition > currentPosition) {
    direction = Direction::UP;
    moveStartTime = millis();

    // Send UP command
    SomfyFrame frame = createFrame(SomfyCommand::UP);
    somfyController.getTransceiver().sendFrame(frame);
  } else {
    direction = Direction::DOWN;
    moveStartTime = millis();

    // Send DOWN command
    SomfyFrame frame = createFrame(SomfyCommand::DOWN);
    somfyController.getTransceiver().sendFrame(frame);
  }
}

void SomfyShade::stop() {
  if (!enabled) return;

  direction = Direction::STOPPED;
  targetPosition = currentPosition;

  // Send STOP command
  SomfyFrame frame = createFrame(SomfyCommand::STOP);
  somfyController.getTransceiver().sendFrame(frame);
}

void SomfyShade::setMyPosition() {
  if (!enabled) return;

  // Send MY command (used for programming)
  SomfyFrame frame = createFrame(SomfyCommand::MY);
  somfyController.getTransceiver().sendFrame(frame);
}

void SomfyShade::update() {
  if (!enabled || direction == Direction::STOPPED) return;

  uint32_t elapsed = millis() - moveStartTime;
  float progress = (float)elapsed / (float)moveDuration;

  if (progress >= 1.0f) {
    // Movement complete
    currentPosition = targetPosition;
    direction = Direction::STOPPED;
  } else {
    // Update current position based on progress
    float startPos = (direction == Direction::UP) ?
                     (targetPosition - (targetPosition - currentPosition)) :
                     (targetPosition + (currentPosition - targetPosition));

    if (direction == Direction::UP) {
      currentPosition = startPos + (targetPosition - startPos) * progress;
    } else {
      currentPosition = startPos - (startPos - targetPosition) * progress;
    }
  }

  // Auto-stop if we've reached the target
  if (abs(currentPosition - targetPosition) < 1.0f) {
    currentPosition = targetPosition;
    direction = Direction::STOPPED;
  }
}

SomfyFrame SomfyShade::createFrame(SomfyCommand command) {
  SomfyFrame frame;

  frame.key = 0xA7;  // Standard Somfy key
  frame.ctrl = static_cast<uint8_t>(command);
  frame.rollingCode = getNextRollingCode();
  frame.address = address;

  // Clear data bytes (not used in basic RTS)
  memset(frame.data, 0, sizeof(frame.data));

  frame.calculateChecksum();
  frame.valid = true;

  return frame;
}

void SomfyShade::save() {
  if (id == 255) return;

  Preferences prefs;
  char key[32];

  snprintf(key, sizeof(key), "shade_%d", id);
  prefs.begin(key, false);

  prefs.putUInt("address", address);
  prefs.putUShort("rollingCode", rollingCode);
  prefs.putString("name", name);
  prefs.putFloat("position", currentPosition);
  prefs.putUInt("duration", moveDuration);
  prefs.putBool("enabled", enabled);

  prefs.end();
}

void SomfyShade::load() {
  if (id == 255) return;

  Preferences prefs;
  char key[32];

  snprintf(key, sizeof(key), "shade_%d", id);
  prefs.begin(key, true);  // Read-only

  address = prefs.getUInt("address", 0);
  rollingCode = prefs.getUShort("rollingCode", 1);
  prefs.getString("name", name, sizeof(name));
  currentPosition = prefs.getFloat("position", 0.0f);
  moveDuration = prefs.getUInt("duration", 20000);
  enabled = prefs.getBool("enabled", false);

  prefs.end();

  // If no address, this shade is not configured
  if (address == 0) {
    clear();
  }
}

// SomfyTransceiver implementation
bool SomfyTransceiver::begin() {
  // Initialize CC1101
  ELECHOUSE_cc1101.Init();

  if (!ELECHOUSE_cc1101.getCC1101()) {
    return false;
  }

  initializeCC1101();
  initialized = true;

  return true;
}

void SomfyTransceiver::end() {
  if (initialized) {
    ELECHOUSE_cc1101.goSleep();
    initialized = false;
  }
}

void SomfyTransceiver::initializeCC1101() {
  // Configure CC1101 for Somfy RTS
  ELECHOUSE_cc1101.setCCMode(1);          // set config for internal transmission mode.
  ELECHOUSE_cc1101.setModulation(0);      // set modulation mode. 0 = 2-FSK, 1 = GFSK, 2 = ASK/OOK, 3 = 4-FSK, 4 = MSK.
  ELECHOUSE_cc1101.setMHZ(frequency);     // Set frequency
  ELECHOUSE_cc1101.setDeviation(47.60);   // Set deviation
  ELECHOUSE_cc1101.setChannel(0);         // Set channel
  ELECHOUSE_cc1101.setChsp(199.95);       // Set channel spacing
  ELECHOUSE_cc1101.setRxBW(325.0);        // Set RX bandwidth
  ELECHOUSE_cc1101.setDRate(3.79372);     // Set data rate
  ELECHOUSE_cc1101.setPA(txPower);        // Set TX power
  ELECHOUSE_cc1101.setSyncMode(2);        // Set sync mode
  ELECHOUSE_cc1101.setSyncWord(211, 145); // Set sync word
  ELECHOUSE_cc1101.setAdrChk(0);          // Set address check
  ELECHOUSE_cc1101.setAddr(0);            // Set address
  ELECHOUSE_cc1101.setWhiteData(0);       // Set white data
  ELECHOUSE_cc1101.setPktFormat(3);       // Set packet format
  ELECHOUSE_cc1101.setLengthConfig(1);    // Set length config
  ELECHOUSE_cc1101.setPacketLength(0);    // Set packet length
  ELECHOUSE_cc1101.setCrc(0);             // Set CRC
  ELECHOUSE_cc1101.setCRC_AF(0);          // Set CRC autoflush
  ELECHOUSE_cc1101.setDcFilterOff(0);     // Set DC filter
  ELECHOUSE_cc1101.setManchester(0);      // Set Manchester encoding
  ELECHOUSE_cc1101.setFEC(0);             // Set FEC
  ELECHOUSE_cc1101.setPRE(0);             // Set preamble
  ELECHOUSE_cc1101.setPQT(0);             // Set PQT
  ELECHOUSE_cc1101.setAppendStatus(0);    // Set append status
}

void SomfyTransceiver::sendFrame(const SomfyFrame& frame, uint8_t repeats) {
  if (!initialized) return;

  uint8_t buffer[15];
  SomfyFrame frameCopy = frame;  // Make a copy since encode is not const
  frameCopy.encode(buffer);

  // Send the frame multiple times as per Somfy protocol
  for (uint8_t i = 0; i < repeats; i++) {
    sendRawData(buffer, sizeof(buffer));

    if (i < repeats - 1) {
      delayMicroseconds(FRAME_REPEAT_DELAY);
    }
  }
}

void SomfyTransceiver::sendRawData(uint8_t* data, uint8_t length) {
  if (!initialized) return;

  // Prepare for transmission
  ELECHOUSE_cc1101.SetTx();

  // Send wakeup sequence
  sendWakeup();

  // Send sync sequence
  sendSync();

  // Send data bits using Manchester-like encoding
  for (uint8_t i = 0; i < length; i++) {
    for (uint8_t bit = 0; bit < 8; bit++) {
      bool bitValue = (data[i] >> (7 - bit)) & 1;
      sendSymbol(bitValue);
    }
  }

  // Return to idle
  ELECHOUSE_cc1101.setSidle();
}

void SomfyTransceiver::sendWakeup() {
  // Send wakeup sequence (high for extended period)
  digitalWrite(12, HIGH);  // GDO2 pin
  delayMicroseconds(9415);  // Somfy wakeup duration
  digitalWrite(12, LOW);
  delayMicroseconds(89565); // Silence after wakeup
}

void SomfyTransceiver::sendSync() {
  // Send sync sequence
  for (int i = 0; i < 2; i++) {
    sendSymbol(true);   // High
    sendSymbol(false);  // Low
  }

  // Hardware sync
  sendSymbol(true);
  delayMicroseconds(4550);  // Extended high for hardware sync
}

void SomfyTransceiver::sendSymbol(bool bit) {
  if (bit) {
    // Send '1' - high then low
    digitalWrite(12, HIGH);  // GDO2 pin
    delayMicroseconds(SYMBOL_DURATION);
    digitalWrite(12, LOW);
    delayMicroseconds(SYMBOL_DURATION);
  } else {
    // Send '0' - low then high
    digitalWrite(12, LOW);   // GDO2 pin
    delayMicroseconds(SYMBOL_DURATION);
    digitalWrite(12, HIGH);
    delayMicroseconds(SYMBOL_DURATION);
  }
}

// SimpleSomfyController implementation
bool SimpleSomfyController::begin() {
  // Initialize preferences
  preferences.begin("somfy", false);

  // Load base address
  baseAddress = preferences.getUInt("baseAddr", 0x120000);

  // Initialize transceiver
  if (!transceiver.begin()) {
    return false;
  }

  // Load shade configurations
  loadConfiguration();

  return true;
}

void SimpleSomfyController::end() {
  saveConfiguration();
  transceiver.end();
  preferences.end();
}

SomfyShade* SimpleSomfyController::addShade(uint32_t address, const char* name) {
  uint8_t slot = findFreeSlot();
  if (slot == 255) return nullptr;  // No free slots

  SomfyShade* shade = &shades[slot];
  shade->setId(slot);
  shade->setAddress(address);
  if (name) shade->setName(name);
  shade->enable(true);

  return shade;
}

SomfyShade* SimpleSomfyController::getShade(uint8_t id) {
  if (id >= MAX_SHADES) return nullptr;
  if (!shades[id].isEnabled()) return nullptr;
  return &shades[id];
}

SomfyShade* SimpleSomfyController::getShadeByAddress(uint32_t address) {
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled() && shades[i].getAddress() == address) {
      return &shades[i];
    }
  }
  return nullptr;
}

bool SimpleSomfyController::removeShade(uint8_t id) {
  if (id >= MAX_SHADES) return false;

  shades[id].clear();

  // Remove from preferences
  Preferences prefs;
  char key[32];
  snprintf(key, sizeof(key), "shade_%d", id);
  prefs.begin(key, false);
  prefs.clear();
  prefs.end();

  return true;
}

uint8_t SimpleSomfyController::getShadeCount() {
  uint8_t count = 0;
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) count++;
  }
  return count;
}

uint8_t SimpleSomfyController::findFreeSlot() {
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (!shades[i].isEnabled()) return i;
  }
  return 255;  // No free slots
}

bool SimpleSomfyController::moveShade(uint8_t id, float position) {
  SomfyShade* shade = getShade(id);
  if (!shade) return false;

  shade->moveToPosition(position);
  return true;
}

bool SimpleSomfyController::stopShade(uint8_t id) {
  SomfyShade* shade = getShade(id);
  if (!shade) return false;

  shade->stop();
  return true;
}

bool SimpleSomfyController::setShadeMyPosition(uint8_t id) {
  SomfyShade* shade = getShade(id);
  if (!shade) return false;

  shade->setMyPosition();
  return true;
}

void SimpleSomfyController::update() {
  // Update all shades
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) {
      shades[i].update();
    }
  }
}

void SimpleSomfyController::saveConfiguration() {
  // Save all shade configurations
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) {
      shades[i].save();
    }
  }

  // Save base address
  preferences.putUInt("baseAddr", baseAddress);
}

void SimpleSomfyController::loadConfiguration() {
  // Load all shade configurations
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    shades[i].setId(i);
    shades[i].load();
  }
}

void SimpleSomfyController::factoryReset() {
  // Clear all shade configurations
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    removeShade(i);
  }

  // Reset base address
  baseAddress = 0x120000;
  preferences.putUInt("baseAddr", baseAddress);
}

void SimpleSomfyController::listShades(JsonDocument& doc) {
  JsonArray shadeArray = doc.createNestedArray("shades");

  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) {
      JsonObject shade = shadeArray.createNestedObject();
      shade["id"] = shades[i].getId();
      shade["name"] = shades[i].getName();
      shade["address"] = shades[i].getAddress();
      shade["position"] = shades[i].getCurrentPosition();
      shade["target"] = shades[i].getTargetPosition();
      shade["direction"] = static_cast<int8_t>(shades[i].getDirection());
      shade["moving"] = shades[i].isMoving();
    }
  }
}

void SimpleSomfyController::getSystemStatus(JsonDocument& doc) {
  doc["initialized"] = transceiver.isInitialized();
  doc["shadeCount"] = getShadeCount();
  doc["maxShades"] = MAX_SHADES;
  doc["baseAddress"] = baseAddress;
  doc["freeHeap"] = ESP.getFreeHeap();
  doc["uptime"] = millis();
}

void SimpleSomfyController::getShadeStatus(uint8_t id, JsonDocument& doc) {
  SomfyShade* shade = getShade(id);
  if (!shade) {
    doc["error"] = "Shade not found";
    return;
  }

  doc["id"] = shade->getId();
  doc["name"] = shade->getName();
  doc["address"] = shade->getAddress();
  doc["position"] = shade->getCurrentPosition();
  doc["target"] = shade->getTargetPosition();
  doc["direction"] = static_cast<int8_t>(shade->getDirection());
  doc["moving"] = shade->isMoving();
  doc["enabled"] = shade->isEnabled();
}

// SimpleSerialInterface implementation
void SimpleSerialInterface::begin(unsigned long baudRate) {
  Serial.begin(baudRate);
  delay(1000);  // Allow serial to initialize
  printWelcome();
}

void SimpleSerialInterface::loop() {
  // Read serial input
  while (Serial.available()) {
    char c = Serial.read();

    if (c == '\n' || c == '\r') {
      if (inputBuffer.length() > 0) {
        commandReady = true;
      }
    } else if (c == '\b' || c == 127) { // Backspace
      if (inputBuffer.length() > 0) {
        inputBuffer.remove(inputBuffer.length() - 1);
        Serial.print("\b \b");
      }
    } else if (c >= 32 && c <= 126) { // Printable characters
      inputBuffer += c;
      Serial.print(c);
    }
  }

  // Process command if ready
  if (commandReady) {
    Serial.println(); // New line

    String command = inputBuffer;
    command.trim();
    inputBuffer = "";
    commandReady = false;

    if (command.length() > 0) {
      processCommand(command);
    }

    Serial.print("> ");
  }

  // Send periodic heartbeat
  if (millis() - lastHeartbeat > 30000) { // Every 30 seconds
    sendHeartbeat();
    lastHeartbeat = millis();
  }
}

void SimpleSerialInterface::processCommand(const String& input) {
  Command cmd = parseCommand(input);

  if (!cmd.valid) {
    sendError("Invalid command format. Type 'help' for usage.");
    return;
  }

  // Execute command
  if (cmd.action == "help") {
    handleHelpCommand();
  } else if (cmd.action == "list") {
    handleListCommand();
  } else if (cmd.action == "system") {
    handleSystemCommand();
  } else if (cmd.action == "move") {
    handleMoveCommand(cmd);
  } else if (cmd.action == "stop") {
    handleStopCommand(cmd);
  } else if (cmd.action == "add") {
    handleAddCommand(cmd);
  } else if (cmd.action == "remove") {
    handleRemoveCommand(cmd);
  } else if (cmd.action == "setname") {
    handleSetNameCommand(cmd);
  } else if (cmd.action == "setmy") {
    handleSetMyCommand(cmd);
  } else if (cmd.action == "reset") {
    handleResetCommand();
  } else {
    sendError("Unknown command: " + cmd.action);
  }
}

Command SimpleSerialInterface::parseCommand(const String& input) {
  // Try JSON format first
  Command cmd = parseJsonCommand(input);
  if (cmd.valid) return cmd;

  // Try text format
  return parseTextCommand(input);
}

Command SimpleSerialInterface::parseTextCommand(const String& input) {
  Command cmd;

  // Split input into tokens
  int spaceIndex = input.indexOf(' ');
  if (spaceIndex == -1) {
    cmd.action = input;
    cmd.action.toLowerCase();
    cmd.valid = (cmd.action == "help" || cmd.action == "list" ||
                 cmd.action == "system" || cmd.action == "reset");
    return cmd;
  }

  cmd.action = input.substring(0, spaceIndex);
  cmd.action.toLowerCase();
  String params = input.substring(spaceIndex + 1);
  params.trim();

  // Parse parameters based on command
  if (cmd.action == "move" || cmd.action == "stop" || cmd.action == "setmy") {
    // Format: move <shadeId> [position]
    int nextSpace = params.indexOf(' ');
    if (nextSpace == -1) {
      cmd.shadeId = params.toInt();
    } else {
      cmd.shadeId = params.substring(0, nextSpace).toInt();
      cmd.position = params.substring(nextSpace + 1).toFloat();
    }
    cmd.valid = (cmd.shadeId < MAX_SHADES);
  } else if (cmd.action == "add") {
    // Format: add <address> [name]
    int nextSpace = params.indexOf(' ');
    if (nextSpace == -1) {
      cmd.address = strtoul(params.c_str(), nullptr, 16);
    } else {
      cmd.address = strtoul(params.substring(0, nextSpace).c_str(), nullptr, 16);
      cmd.name = params.substring(nextSpace + 1);
      cmd.name.replace("\"", ""); // Remove quotes
    }
    cmd.valid = (cmd.address != 0);
  } else if (cmd.action == "remove") {
    // Format: remove <shadeId>
    cmd.shadeId = params.toInt();
    cmd.valid = (cmd.shadeId < MAX_SHADES);
  } else if (cmd.action == "setname") {
    // Format: setname <shadeId> <name>
    int nextSpace = params.indexOf(' ');
    if (nextSpace != -1) {
      cmd.shadeId = params.substring(0, nextSpace).toInt();
      cmd.name = params.substring(nextSpace + 1);
      cmd.name.replace("\"", ""); // Remove quotes
      cmd.valid = (cmd.shadeId < MAX_SHADES && cmd.name.length() > 0);
    }
  }

  return cmd;
}

Command SimpleSerialInterface::parseJsonCommand(const String& input) {
  Command cmd;

  DynamicJsonDocument doc(1024);
  DeserializationError error = deserializeJson(doc, input);

  if (error) {
    return cmd; // Invalid JSON
  }

  if (doc.containsKey("action")) {
    cmd.action = doc["action"].as<String>();
    cmd.action.toLowerCase();
  } else {
    return cmd; // No action specified
  }

  if (doc.containsKey("shadeId")) {
    cmd.shadeId = doc["shadeId"];
  }

  if (doc.containsKey("position")) {
    cmd.position = doc["position"];
  }

  if (doc.containsKey("address")) {
    cmd.address = doc["address"];
  }

  if (doc.containsKey("name")) {
    cmd.name = doc["name"].as<String>();
  }

  cmd.valid = true;
  return cmd;
}

void SimpleSerialInterface::sendError(const String& message) {
  DynamicJsonDocument doc(512);
  doc["type"] = "error";
  doc["success"] = false;
  doc["timestamp"] = millis();
  doc["message"] = message;
  sendJson(doc);
}

void SimpleSerialInterface::sendSuccess(const String& message) {
  DynamicJsonDocument doc(512);
  doc["type"] = "success";
  doc["success"] = true;
  doc["timestamp"] = millis();
  doc["message"] = message;
  sendJson(doc);
}

void SimpleSerialInterface::sendJson(JsonDocument& doc) {
  serializeJson(doc, Serial);
  Serial.println();
}

void SimpleSerialInterface::printWelcome() {
  Serial.println();
  Serial.println("=== Simple Somfy RTS Controller ===");
  Serial.println("Version 1.0 - Serial Interface");
  Serial.println("Type 'help' for available commands");
  Serial.println();
  Serial.print("> ");
}

void SimpleSerialInterface::printHelp() {
  Serial.println("Available Commands:");
  Serial.println();
  Serial.println("System Commands:");
  Serial.println("  help                    - Show this help");
  Serial.println("  system                  - Show system status");
  Serial.println("  list                    - List all shades");
  Serial.println("  reset                   - Factory reset (removes all shades)");
  Serial.println();
  Serial.println("Shade Commands:");
  Serial.println("  add <addr> [name]       - Add shade (addr in hex)");
  Serial.println("  remove <id>             - Remove shade");
  Serial.println("  move <id> <pos>         - Move shade to position (0-100)");
  Serial.println("  stop <id>               - Stop shade movement");
  Serial.println("  setname <id> <name>     - Set shade name");
  Serial.println("  setmy <id>              - Set MY position (for programming)");
  Serial.println();
  Serial.println("JSON Format:");
  Serial.println("  {\"action\":\"move\",\"shadeId\":0,\"position\":50}");
  Serial.println("  {\"action\":\"add\",\"address\":1179649,\"name\":\"Living Room\"}");
  Serial.println();
  Serial.println("Examples:");
  Serial.println("  add 120001 \"Living Room\"");
  Serial.println("  move 0 75");
  Serial.println("  stop 0");
}

void SimpleSerialInterface::notifyShadeStateChanged(uint8_t shadeId) {
  SomfyShade* shade = somfyController.getShade(shadeId);
  if (!shade) return;

  DynamicJsonDocument doc(512);
  doc["type"] = "stateChanged";
  doc["success"] = true;
  doc["timestamp"] = millis();
  doc["shadeId"] = shadeId;
  doc["position"] = shade->getCurrentPosition();
  doc["target"] = shade->getTargetPosition();
  doc["direction"] = static_cast<int8_t>(shade->getDirection());
  doc["moving"] = shade->isMoving();

  sendJson(doc);
}

void SimpleSerialInterface::sendHeartbeat() {
  DynamicJsonDocument doc(256);
  doc["type"] = "heartbeat";
  doc["success"] = true;
  doc["timestamp"] = millis();
  doc["uptime"] = millis();
  doc["freeHeap"] = ESP.getFreeHeap();

  sendJson(doc);
}

// Command handlers
void SimpleSerialInterface::handleListCommand() {
  DynamicJsonDocument doc(2048);
  doc["type"] = "shadeList";
  doc["success"] = true;
  doc["timestamp"] = millis();

  somfyController.listShades(doc);
  sendJson(doc);
}

void SimpleSerialInterface::handleSystemCommand() {
  DynamicJsonDocument doc(1024);
  doc["type"] = "systemStatus";
  doc["success"] = true;
  doc["timestamp"] = millis();

  somfyController.getSystemStatus(doc);
  sendJson(doc);
}

void SimpleSerialInterface::handleMoveCommand(const Command& cmd) {
  if (cmd.position < 0 || cmd.position > 100) {
    sendError("Position must be between 0 and 100");
    return;
  }

  if (somfyController.moveShade(cmd.shadeId, cmd.position)) {
    sendSuccess("Moving shade " + String(cmd.shadeId) + " to position " + String(cmd.position));
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleStopCommand(const Command& cmd) {
  if (somfyController.stopShade(cmd.shadeId)) {
    sendSuccess("Stopped shade " + String(cmd.shadeId));
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleAddCommand(const Command& cmd) {
  SomfyShade* shade = somfyController.addShade(cmd.address, cmd.name.c_str());
  if (shade) {
    somfyController.saveConfiguration();
    sendSuccess("Added shade " + String(shade->getId()) + " with address 0x" + String(cmd.address, HEX));
  } else {
    sendError("Failed to add shade - maximum number reached or invalid address");
  }
}

void SimpleSerialInterface::handleRemoveCommand(const Command& cmd) {
  if (somfyController.removeShade(cmd.shadeId)) {
    somfyController.saveConfiguration();
    sendSuccess("Removed shade " + String(cmd.shadeId));
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleSetNameCommand(const Command& cmd) {
  SomfyShade* shade = somfyController.getShade(cmd.shadeId);
  if (!shade) {
    sendError("Shade " + String(cmd.shadeId) + " not found");
    return;
  }

  shade->setName(cmd.name.c_str());
  shade->save();
  sendSuccess("Set name for shade " + String(cmd.shadeId) + " to: " + cmd.name);
}

void SimpleSerialInterface::handleSetMyCommand(const Command& cmd) {
  if (somfyController.setShadeMyPosition(cmd.shadeId)) {
    sendSuccess("Sent MY command to shade " + String(cmd.shadeId));
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleHelpCommand() {
  printHelp();
}

void SimpleSerialInterface::handleResetCommand() {
  somfyController.factoryReset();
  sendSuccess("Factory reset completed - all shades removed");
}
