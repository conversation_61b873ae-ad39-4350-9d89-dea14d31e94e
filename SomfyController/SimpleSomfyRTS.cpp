#include "SimpleSomfyRTS.h"

// Global instance
SimpleSomfyController somfyController;

// SomfyFrame implementation
void SomfyFrame::calculateChecksum() {
  checksum = 0;
  checksum ^= key;
  checksum ^= ctrl;
  checksum ^= (rollingCode >> 8) & 0xFF;
  checksum ^= rollingCode & 0xFF;
  checksum ^= (address >> 16) & 0xFF;
  checksum ^= (address >> 8) & 0xFF;
  checksum ^= address & 0xFF;
  for (int i = 0; i < 7; i++) {
    checksum ^= data[i];
  }
}

void SomfyFrame::encode(uint8_t* buffer) {
  calculateChecksum();
  
  buffer[0] = key;
  buffer[1] = ctrl;
  buffer[2] = checksum;
  buffer[3] = (rollingCode >> 8) & 0xFF;
  buffer[4] = rollingCode & 0xFF;
  buffer[5] = (address >> 16) & 0xFF;
  buffer[6] = (address >> 8) & 0xFF;
  buffer[7] = address & 0xFF;
  
  // Copy data bytes
  for (int i = 0; i < 7; i++) {
    buffer[8 + i] = data[i];
  }
}

bool SomfyFrame::decode(uint8_t* buffer) {
  key = buffer[0];
  ctrl = buffer[1];
  uint8_t receivedChecksum = buffer[2];
  rollingCode = (buffer[3] << 8) | buffer[4];
  address = (buffer[5] << 16) | (buffer[6] << 8) | buffer[7];
  
  for (int i = 0; i < 7; i++) {
    data[i] = buffer[8 + i];
  }
  
  calculateChecksum();
  valid = (checksum == receivedChecksum);
  return valid;
}

// SomfyShade implementation
SomfyShade::SomfyShade() {
  clear();
}

void SomfyShade::clear() {
  id = 255;
  address = 0;
  rollingCode = 1;
  memset(name, 0, sizeof(name));
  currentPosition = 0.0f;
  targetPosition = 0.0f;
  direction = Direction::STOPPED;
  moveStartTime = 0;
  moveDuration = 20000;  // 20 seconds default
  enabled = false;
}

void SomfyShade::setId(uint8_t shadeId) {
  id = shadeId;
}

void SomfyShade::setAddress(uint32_t addr) {
  address = addr;
}

void SomfyShade::setName(const char* shadeName) {
  if (shadeName) {
    strncpy(name, shadeName, sizeof(name) - 1);
    name[sizeof(name) - 1] = '\0';
  }
}

void SomfyShade::setMoveDuration(uint32_t duration) {
  moveDuration = duration;
}

void SomfyShade::enable(bool state) {
  enabled = state;
}

void SomfyShade::moveToPosition(float position) {
  if (!enabled) return;
  
  // Clamp position to valid range
  if (position < 0.0f) position = 0.0f;
  if (position > 100.0f) position = 100.0f;
  
  targetPosition = position;
  
  if (abs(targetPosition - currentPosition) < 1.0f) {
    // Already at target
    direction = Direction::STOPPED;
    return;
  }
  
  // Determine direction
  if (targetPosition > currentPosition) {
    direction = Direction::UP;
    moveStartTime = millis();
    
    // Send UP command
    SomfyFrame frame = createFrame(SomfyCommand::UP);
    somfyController.getTransceiver().sendFrame(frame);
  } else {
    direction = Direction::DOWN;
    moveStartTime = millis();
    
    // Send DOWN command
    SomfyFrame frame = createFrame(SomfyCommand::DOWN);
    somfyController.getTransceiver().sendFrame(frame);
  }
}

void SomfyShade::moveUp() {
  moveToPosition(100.0f);
}

void SomfyShade::moveDown() {
  moveToPosition(0.0f);
}

void SomfyShade::stop() {
  if (!enabled) return;
  
  direction = Direction::STOPPED;
  targetPosition = currentPosition;
  
  // Send STOP command
  SomfyFrame frame = createFrame(SomfyCommand::STOP);
  somfyController.getTransceiver().sendFrame(frame);
}

void SomfyShade::setMyPosition() {
  if (!enabled) return;
  
  // Send MY command (used for programming)
  SomfyFrame frame = createFrame(SomfyCommand::MY);
  somfyController.getTransceiver().sendFrame(frame);
}

void SomfyShade::update() {
  if (!enabled || direction == Direction::STOPPED) return;
  
  uint32_t elapsed = millis() - moveStartTime;
  float progress = (float)elapsed / (float)moveDuration;
  
  if (progress >= 1.0f) {
    // Movement complete
    currentPosition = targetPosition;
    direction = Direction::STOPPED;
  } else {
    // Update current position based on progress
    float startPos = (direction == Direction::UP) ? 
                     (targetPosition - (targetPosition - currentPosition)) : 
                     (targetPosition + (currentPosition - targetPosition));
    
    if (direction == Direction::UP) {
      currentPosition = startPos + (targetPosition - startPos) * progress;
    } else {
      currentPosition = startPos - (startPos - targetPosition) * progress;
    }
  }
  
  // Auto-stop if we've reached the target
  if (abs(currentPosition - targetPosition) < 1.0f) {
    currentPosition = targetPosition;
    direction = Direction::STOPPED;
  }
}

void SomfyShade::setCurrentPosition(float position) {
  if (position < 0.0f) position = 0.0f;
  if (position > 100.0f) position = 100.0f;
  currentPosition = position;
}

SomfyFrame SomfyShade::createFrame(SomfyCommand command) {
  SomfyFrame frame;
  
  frame.key = 0xA7;  // Standard Somfy key
  frame.ctrl = static_cast<uint8_t>(command);
  frame.rollingCode = getNextRollingCode();
  frame.address = address;
  
  // Clear data bytes (not used in basic RTS)
  memset(frame.data, 0, sizeof(frame.data));
  
  frame.calculateChecksum();
  frame.valid = true;
  
  return frame;
}

uint16_t SomfyShade::getNextRollingCode() {
  rollingCode++;
  if (rollingCode == 0) rollingCode = 1;  // Never use 0
  return rollingCode;
}

void SomfyShade::save() {
  if (id == 255) return;
  
  Preferences prefs;
  char key[32];
  
  snprintf(key, sizeof(key), "shade_%d", id);
  prefs.begin(key, false);
  
  prefs.putUInt("address", address);
  prefs.putUShort("rollingCode", rollingCode);
  prefs.putString("name", name);
  prefs.putFloat("position", currentPosition);
  prefs.putUInt("duration", moveDuration);
  prefs.putBool("enabled", enabled);
  
  prefs.end();
}

void SomfyShade::load() {
  if (id == 255) return;
  
  Preferences prefs;
  char key[32];
  
  snprintf(key, sizeof(key), "shade_%d", id);
  prefs.begin(key, true);  // Read-only
  
  address = prefs.getUInt("address", 0);
  rollingCode = prefs.getUShort("rollingCode", 1);
  prefs.getString("name", name, sizeof(name));
  currentPosition = prefs.getFloat("position", 0.0f);
  moveDuration = prefs.getUInt("duration", 20000);
  enabled = prefs.getBool("enabled", false);
  
  prefs.end();
  
  // If no address, this shade is not configured
  if (address == 0) {
    clear();
  }
}

// SomfyTransceiver implementation
SomfyTransceiver::SomfyTransceiver() {
  initialized = false;
  frequency = 433.92f;
  txPower = 10;
  
  // Default ESP32 pins
  sckPin = 18;
  misoPin = 19;
  mosiPin = 23;
  csPin = 5;
  gdo0Pin = 14;
  gdo2Pin = 12;
}

void SomfyTransceiver::setPins(uint8_t sck, uint8_t miso, uint8_t mosi, uint8_t cs, uint8_t gdo0, uint8_t gdo2) {
  sckPin = sck;
  misoPin = miso;
  mosiPin = mosi;
  csPin = cs;
  gdo0Pin = gdo0;
  gdo2Pin = gdo2;
}

void SomfyTransceiver::setFrequency(float freq) {
  frequency = freq;
}

void SomfyTransceiver::setTxPower(int8_t power) {
  txPower = power;
}

bool SomfyTransceiver::begin() {
  // Initialize SPI pins
  pinMode(csPin, OUTPUT);
  pinMode(gdo0Pin, INPUT);
  pinMode(gdo2Pin, OUTPUT);
  
  // Initialize CC1101
  ELECHOUSE_cc1101.Init();
  
  if (!ELECHOUSE_cc1101.getCC1101()) {
    return false;
  }
  
  initializeCC1101();
  initialized = true;
  
  return true;
}

void SomfyTransceiver::end() {
  if (initialized) {
    ELECHOUSE_cc1101.goSleep();
    initialized = false;
  }
}

void SomfyTransceiver::initializeCC1101() {
  // Configure CC1101 for Somfy RTS
  ELECHOUSE_cc1101.setCCMode(1);          // set config for internal transmission mode.
  ELECHOUSE_cc1101.setModulation(0);      // set modulation mode. 0 = 2-FSK, 1 = GFSK, 2 = ASK/OOK, 3 = 4-FSK, 4 = MSK.
  ELECHOUSE_cc1101.setMHZ(frequency);     // Set frequency
  ELECHOUSE_cc1101.setDeviation(47.60);   // Set deviation
  ELECHOUSE_cc1101.setChannel(0);         // Set channel
  ELECHOUSE_cc1101.setChsp(199.95);       // Set channel spacing
  ELECHOUSE_cc1101.setRxBW(325.0);        // Set RX bandwidth
  ELECHOUSE_cc1101.setDRate(3.79372);     // Set data rate
  ELECHOUSE_cc1101.setPA(txPower);        // Set TX power
  ELECHOUSE_cc1101.setSyncMode(2);        // Set sync mode
  ELECHOUSE_cc1101.setSyncWord(211, 145); // Set sync word
  ELECHOUSE_cc1101.setAdrChk(0);          // Set address check
  ELECHOUSE_cc1101.setAddr(0);            // Set address
  ELECHOUSE_cc1101.setWhiteData(0);       // Set white data
  ELECHOUSE_cc1101.setPktFormat(3);       // Set packet format
  ELECHOUSE_cc1101.setLengthConfig(1);    // Set length config
  ELECHOUSE_cc1101.setPacketLength(0);    // Set packet length
  ELECHOUSE_cc1101.setCrc(0);             // Set CRC
  ELECHOUSE_cc1101.setCRC_AF(0);          // Set CRC autoflush
  ELECHOUSE_cc1101.setDcFilterOff(0);     // Set DC filter
  ELECHOUSE_cc1101.setManchester(0);      // Set Manchester encoding
  ELECHOUSE_cc1101.setFEC(0);             // Set FEC
  ELECHOUSE_cc1101.setPRE(0);             // Set preamble
  ELECHOUSE_cc1101.setPQT(0);             // Set PQT
  ELECHOUSE_cc1101.setAppendStatus(0);    // Set append status
}

void SomfyTransceiver::sendFrame(const SomfyFrame& frame, uint8_t repeats) {
  if (!initialized) return;

  uint8_t buffer[15];
  SomfyFrame frameCopy = frame;  // Make a copy since encode is not const
  frameCopy.encode(buffer);

  // Send the frame multiple times as per Somfy protocol
  for (uint8_t i = 0; i < repeats; i++) {
    sendRawData(buffer, sizeof(buffer));

    if (i < repeats - 1) {
      delayMicroseconds(FRAME_REPEAT_DELAY);
    }
  }
}

void SomfyTransceiver::sendRawData(uint8_t* data, uint8_t length) {
  if (!initialized) return;

  // Prepare for transmission
  ELECHOUSE_cc1101.SetTx();

  // Send wakeup sequence
  sendWakeup();

  // Send sync sequence
  sendSync();

  // Encode and send data using Manchester encoding
  uint8_t encodedData[64];
  uint8_t encodedLength;
  encodeManchester(data, length, encodedData, encodedLength);

  // Send encoded data
  for (uint8_t i = 0; i < encodedLength; i++) {
    for (uint8_t bit = 0; bit < 8; bit++) {
      bool bitValue = (encodedData[i] >> (7 - bit)) & 1;
      sendSymbol(bitValue);
    }
  }

  // Return to idle
  ELECHOUSE_cc1101.setSidle();
}

void SomfyTransceiver::sendWakeup() {
  // Send wakeup sequence (high for extended period)
  digitalWrite(gdo2Pin, HIGH);
  delayMicroseconds(9415);  // Somfy wakeup duration
  digitalWrite(gdo2Pin, LOW);
  delayMicroseconds(89565); // Silence after wakeup
}

void SomfyTransceiver::sendSync() {
  // Send sync sequence
  for (int i = 0; i < 2; i++) {
    sendSymbol(true);   // High
    sendSymbol(false);  // Low
  }

  // Hardware sync
  sendSymbol(true);
  delayMicroseconds(4550);  // Extended high for hardware sync
}

void SomfyTransceiver::sendSymbol(bool bit) {
  if (bit) {
    // Send '1' - high then low
    digitalWrite(gdo2Pin, HIGH);
    delayMicroseconds(SYMBOL_DURATION);
    digitalWrite(gdo2Pin, LOW);
    delayMicroseconds(SYMBOL_DURATION);
  } else {
    // Send '0' - low then high
    digitalWrite(gdo2Pin, LOW);
    delayMicroseconds(SYMBOL_DURATION);
    digitalWrite(gdo2Pin, HIGH);
    delayMicroseconds(SYMBOL_DURATION);
  }
}

void SomfyTransceiver::encodeManchester(uint8_t* input, uint8_t inputLen, uint8_t* output, uint8_t& outputLen) {
  outputLen = 0;
  uint8_t currentByte = 0;
  uint8_t bitCount = 0;

  for (uint8_t i = 0; i < inputLen; i++) {
    for (uint8_t bit = 0; bit < 8; bit++) {
      bool inputBit = (input[i] >> (7 - bit)) & 1;

      // Manchester encoding: 0 -> 01, 1 -> 10
      if (inputBit) {
        // Encode '1' as '10'
        currentByte = (currentByte << 1) | 1;
        bitCount++;
        if (bitCount == 8) {
          output[outputLen++] = currentByte;
          currentByte = 0;
          bitCount = 0;
        }

        currentByte = (currentByte << 1) | 0;
        bitCount++;
        if (bitCount == 8) {
          output[outputLen++] = currentByte;
          currentByte = 0;
          bitCount = 0;
        }
      } else {
        // Encode '0' as '01'
        currentByte = (currentByte << 1) | 0;
        bitCount++;
        if (bitCount == 8) {
          output[outputLen++] = currentByte;
          currentByte = 0;
          bitCount = 0;
        }

        currentByte = (currentByte << 1) | 1;
        bitCount++;
        if (bitCount == 8) {
          output[outputLen++] = currentByte;
          currentByte = 0;
          bitCount = 0;
        }
      }
    }
  }

  // Handle remaining bits
  if (bitCount > 0) {
    currentByte <<= (8 - bitCount);
    output[outputLen++] = currentByte;
  }
}

// SimpleSomfyController implementation
SimpleSomfyController::SimpleSomfyController() {
  baseAddress = 0x120000;  // Default base address
}

bool SimpleSomfyController::begin() {
  // Initialize preferences
  preferences.begin("somfy", false);

  // Load base address
  baseAddress = preferences.getUInt("baseAddr", 0x120000);

  // Initialize transceiver
  if (!transceiver.begin()) {
    return false;
  }

  // Load shade configurations
  loadConfiguration();

  return true;
}

void SimpleSomfyController::end() {
  saveConfiguration();
  transceiver.end();
  preferences.end();
}

SomfyShade* SimpleSomfyController::addShade(uint32_t address, const char* name) {
  uint8_t slot = findFreeSlot();
  if (slot == 255) return nullptr;  // No free slots

  SomfyShade* shade = &shades[slot];
  shade->setId(slot);
  shade->setAddress(address);
  if (name) shade->setName(name);
  shade->enable(true);

  return shade;
}

SomfyShade* SimpleSomfyController::getShade(uint8_t id) {
  if (id >= MAX_SHADES) return nullptr;
  if (!shades[id].isEnabled()) return nullptr;
  return &shades[id];
}

SomfyShade* SimpleSomfyController::getShadeByAddress(uint32_t address) {
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled() && shades[i].getAddress() == address) {
      return &shades[i];
    }
  }
  return nullptr;
}

bool SimpleSomfyController::removeShade(uint8_t id) {
  if (id >= MAX_SHADES) return false;

  shades[id].clear();

  // Remove from preferences
  Preferences prefs;
  char key[32];
  snprintf(key, sizeof(key), "shade_%d", id);
  prefs.begin(key, false);
  prefs.clear();
  prefs.end();

  return true;
}

uint8_t SimpleSomfyController::getShadeCount() {
  uint8_t count = 0;
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) count++;
  }
  return count;
}

uint8_t SimpleSomfyController::findFreeSlot() {
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (!shades[i].isEnabled()) return i;
  }
  return 255;  // No free slots
}

bool SimpleSomfyController::moveShade(uint8_t id, float position) {
  SomfyShade* shade = getShade(id);
  if (!shade) return false;

  shade->moveToPosition(position);
  return true;
}

bool SimpleSomfyController::stopShade(uint8_t id) {
  SomfyShade* shade = getShade(id);
  if (!shade) return false;

  shade->stop();
  return true;
}

bool SimpleSomfyController::setShadeMyPosition(uint8_t id) {
  SomfyShade* shade = getShade(id);
  if (!shade) return false;

  shade->setMyPosition();
  return true;
}

void SimpleSomfyController::update() {
  // Update all shades
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) {
      shades[i].update();
    }
  }
}

void SimpleSomfyController::saveConfiguration() {
  // Save all shade configurations
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) {
      shades[i].save();
    }
  }

  // Save base address
  preferences.putUInt("baseAddr", baseAddress);
}

void SimpleSomfyController::loadConfiguration() {
  // Load all shade configurations
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    shades[i].setId(i);
    shades[i].load();
  }
}

void SimpleSomfyController::factoryReset() {
  // Clear all shade configurations
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    removeShade(i);
  }

  // Reset base address
  baseAddress = 0x120000;
  preferences.putUInt("baseAddr", baseAddress);
}

void SimpleSomfyController::listShades(JsonDocument& doc) {
  JsonArray shadeArray = doc.createNestedArray("shades");

  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    if (shades[i].isEnabled()) {
      JsonObject shade = shadeArray.createNestedObject();
      shade["id"] = shades[i].getId();
      shade["name"] = shades[i].getName();
      shade["address"] = shades[i].getAddress();
      shade["position"] = shades[i].getCurrentPosition();
      shade["target"] = shades[i].getTargetPosition();
      shade["direction"] = static_cast<int8_t>(shades[i].getDirection());
      shade["moving"] = shades[i].isMoving();
    }
  }
}

void SimpleSomfyController::getSystemStatus(JsonDocument& doc) {
  doc["initialized"] = transceiver.isInitialized();
  doc["shadeCount"] = getShadeCount();
  doc["maxShades"] = MAX_SHADES;
  doc["baseAddress"] = baseAddress;
  doc["freeHeap"] = ESP.getFreeHeap();
  doc["uptime"] = millis();
}

void SimpleSomfyController::getShadeStatus(uint8_t id, JsonDocument& doc) {
  SomfyShade* shade = getShade(id);
  if (!shade) {
    doc["error"] = "Shade not found";
    return;
  }

  doc["id"] = shade->getId();
  doc["name"] = shade->getName();
  doc["address"] = shade->getAddress();
  doc["position"] = shade->getCurrentPosition();
  doc["target"] = shade->getTargetPosition();
  doc["direction"] = static_cast<int8_t>(shade->getDirection());
  doc["moving"] = shade->isMoving();
  doc["enabled"] = shade->isEnabled();
}
