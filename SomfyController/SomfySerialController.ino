/*
 * Somfy RTS Serial Controller
 * 
 * A simplified serial interface for controlling Somfy RTS shades
 * without WiFi/MQTT dependencies.
 * 
 * Hardware Requirements:
 * - ESP32 development board
 * - CC1101 transceiver module
 * 
 * Wiring (default pins):
 * CC1101    ESP32
 * VCC   ->  3.3V
 * GND   ->  GND
 * MOSI  ->  GPIO23
 * MISO  ->  GPIO19
 * SCK   ->  GPIO18
 * CSN   ->  GPIO5
 * GDO0  ->  GPIO14 (RX)
 * GDO2  ->  GPIO12 (TX)
 */

#include <Arduino.h>
#include <esp_task_wdt.h>
#include "SomfySerialInterface.h"

// Global controller instance (defined in Somfy.cpp)
extern SomfyShadeController somfy;

void setup() {
  // Initialize watchdog timer for ESP32 v5.x
  esp_task_wdt_config_t wdt_config = {
    .timeout_ms = 7000,
    .idle_core_mask = (1 << portNUM_PROCESSORS) - 1,
    .trigger_panic = true
  };
  esp_task_wdt_init(&wdt_config);
  esp_task_wdt_add(NULL);
  
  // Initialize serial interface
  serialInterface.begin(115200);
  
  // Initialize Somfy controller
  if (!somfy.begin()) {
    Serial.println("ERROR: Failed to initialize Somfy controller");
    Serial.println("Check CC1101 wiring and connections");
    while (true) {
      delay(1000);
      esp_task_wdt_reset();
    }
  }
  
  Serial.println("Somfy RTS Serial Controller initialized successfully");
  Serial.println("CC1101 transceiver ready");
  Serial.println("Type 'help' for available commands");
}

void loop() {
  // Reset watchdog timer
  esp_task_wdt_reset();
  
  // Process serial interface
  serialInterface.loop();
  
  // Process Somfy controller
  somfy.loop();
  
  // Small delay to prevent watchdog timeout
  delay(1);
}
