#ifndef SERIAL_INTERFACE_H
#define SERIAL_INTERFACE_H

#include <Arduino.h>
#include "Somfy.h"

class SerialInterface {
  private:
    char inputBuffer[128];
    uint8_t bufferIndex = 0;
    bool commandReady = false;

    // Pairing mode variables
    bool pairingMode = false;
    uint8_t pairingShadeId = 0;
    uint32_t pairingStartTime = 0;

    // Listening mode variables
    bool listeningMode = false;
    uint32_t listeningStartTime = 0;

    // Scanning mode variables
    bool scanningMode = false;
    uint32_t scanningStartTime = 0;

    // Discovered remotes tracking
    struct DiscoveredRemote {
      uint32_t address;
      uint16_t lastRollingCode;
      uint32_t lastSeen;
      uint8_t commandCount;
    };
    static const uint8_t MAX_DISCOVERED_REMOTES = 16;
    DiscoveredRemote discoveredRemotes[MAX_DISCOVERED_REMOTES];
    uint8_t discoveredRemoteCount = 0;
    
    // Command parsing helpers
    void parseCommand(const char* command);
    void printHelp();
    void printShadeList();
    void printShadeInfo(uint8_t shadeId);
    void printRemoteInfo(uint32_t address, uint16_t rollingCode, somfy_commands cmd);
    
    // Command handlers
    void handleShadeCommand(uint8_t shadeId, const char* command);
    void handlePairCommand(const char* params);
    void handleListCommand(const char* params);
    void handleInfoCommand(const char* params);
    void handleAddCommand(const char* params);
    void handleDeleteCommand(const char* params);
    void handleSetCommand(const char* params);
    void handleUnpairCommand(const char* params);
    void handleListenCommand(const char* params);
    void handleScanCommand(const char* params);
    void handleTestCommand(const char* params);
    void handleStatusCommand();

    // Utility functions
    bool validateShadeCommand(uint8_t shadeId, const char* command);
    void handleShadeCommandEnhanced(uint8_t shadeId, const char* command);
    
    // Utility functions
    uint8_t parseShadeId(const char* str);
    float parseFloat(const char* str);
    bool isValidShadeId(uint8_t shadeId);
    
  public:
    SerialInterface();
    void begin(uint32_t baudRate = 115200);
    void loop();
    void processInput();
    
    // Event handlers for remote discovery
    void onRemoteReceived(uint32_t address, uint16_t rollingCode, somfy_commands cmd, int8_t rssi);
    void onShadeStateChanged(uint8_t shadeId, float position, float tiltPosition, int8_t direction);
    void onShadePaired(uint8_t shadeId, uint32_t address);
};

#endif // SERIAL_INTERFACE_H
