#include "SomfySerialInterface.h"
#include "Somfy.h"

// Global instance
SerialInterface serialInterface(&somfy);

SerialInterface::SerialInterface(SomfyShadeController* ctrl) 
  : controller(ctrl), commandReady(false) {
}

void SerialInterface::begin(unsigned long baudRate) {
  Serial.begin(baudRate);
  Serial.println("Somfy RTS Serial Interface v1.0");
  Serial.println("Type 'help' for available commands");
  Serial.print("> ");
}

void SerialInterface::loop() {
  // Read serial input
  while (Serial.available()) {
    char c = Serial.read();
    
    if (c == '\n' || c == '\r') {
      if (inputBuffer.length() > 0) {
        commandReady = true;
      }
    } else if (c == '\b' || c == 127) { // Backspace
      if (inputBuffer.length() > 0) {
        inputBuffer.remove(inputBuffer.length() - 1);
        Serial.print("\b \b");
      }
    } else if (c >= 32 && c <= 126) { // Printable characters
      inputBuffer += c;
      Serial.print(c);
    }
  }
  
  // Process command if ready
  if (commandReady) {
    Serial.println(); // New line
    
    String command = inputBuffer;
    command.trim();
    inputBuffer = "";
    commandReady = false;
    
    if (command.length() > 0) {
      processCommand(command);
    }
    
    Serial.print("> ");
  }
}

void SerialInterface::processCommand(const String& input) {
  // Try JSON format first
  SerialCommand cmd = parseCommand(input);
  
  if (!cmd.valid) {
    // Try simple text commands
    cmd = parseTextCommand(input);
  }
  
  if (!cmd.valid) {
    sendError("Invalid command format. Type 'help' for usage.");
    return;
  }
  
  // Execute command
  if (cmd.command == "help") {
    printHelp();
  } else if (cmd.command == "status") {
    printStatus();
  } else if (cmd.command == "list") {
    handleListShadesCommand();
  } else if (cmd.command == "move") {
    handleMoveCommand(cmd);
  } else if (cmd.command == "stop") {
    handleStopCommand(cmd);
  } else if (cmd.command == "setmy") {
    handleSetMyCommand(cmd);
  } else if (cmd.command == "getstatus") {
    handleGetStatusCommand(cmd);
  } else if (cmd.command == "add") {
    handleAddShadeCommand(cmd);
  } else if (cmd.command == "remove") {
    handleRemoveShadeCommand(cmd);
  } else if (cmd.command == "setname") {
    handleSetNameCommand(cmd);
  } else {
    sendError("Unknown command: " + cmd.command);
  }
}

SerialCommand SerialInterface::parseCommand(const String& input) {
  SerialCommand cmd;
  
  // Try JSON parsing first
  if (parseJson(input, cmd)) {
    return cmd;
  }
  
  return cmd; // Invalid
}

SerialCommand SerialInterface::parseTextCommand(const String& input) {
  SerialCommand cmd;
  
  // Split input into tokens
  int spaceIndex = input.indexOf(' ');
  if (spaceIndex == -1) {
    cmd.command = input;
    cmd.command.toLowerCase();
    cmd.valid = (cmd.command == "help" || cmd.command == "status" || cmd.command == "list");
    return cmd;
  }
  
  cmd.command = input.substring(0, spaceIndex);
  cmd.command.toLowerCase();
  String params = input.substring(spaceIndex + 1);
  params.trim();
  
  // Parse parameters based on command
  if (cmd.command == "move" || cmd.command == "stop" || cmd.command == "getstatus") {
    // Format: move <shadeId> [position] [tiltPosition]
    int nextSpace = params.indexOf(' ');
    if (nextSpace == -1) {
      cmd.shadeId = params.toInt();
    } else {
      cmd.shadeId = params.substring(0, nextSpace).toInt();
      String remaining = params.substring(nextSpace + 1);
      remaining.trim();
      
      int nextSpace2 = remaining.indexOf(' ');
      if (nextSpace2 == -1) {
        cmd.position = remaining.toFloat();
      } else {
        cmd.position = remaining.substring(0, nextSpace2).toFloat();
        cmd.tiltPosition = remaining.substring(nextSpace2 + 1).toFloat();
      }
    }
    cmd.valid = (cmd.shadeId > 0 && cmd.shadeId < 255);
  }
  
  return cmd;
}

bool SerialInterface::parseJson(const String& input, SerialCommand& cmd) {
  DynamicJsonDocument doc(1024);
  DeserializationError error = deserializeJson(doc, input);
  
  if (error) {
    return false;
  }
  
  if (doc.containsKey("command")) {
    cmd.command = doc["command"].as<String>();
    cmd.command.toLowerCase();
  } else {
    return false;
  }
  
  if (doc.containsKey("shadeId")) {
    cmd.shadeId = doc["shadeId"];
  }
  
  if (doc.containsKey("position")) {
    cmd.position = doc["position"];
  }
  
  if (doc.containsKey("tiltPosition")) {
    cmd.tiltPosition = doc["tiltPosition"];
  }
  
  if (doc.containsKey("remoteAddress")) {
    cmd.remoteAddress = doc["remoteAddress"];
  }
  
  if (doc.containsKey("name")) {
    cmd.name = doc["name"].as<String>();
  }
  
  cmd.valid = true;
  return true;
}

void SerialInterface::sendResponse(const SerialResponse& response) {
  DynamicJsonDocument doc(1024);
  
  doc["type"] = response.type;
  doc["success"] = response.success;
  
  if (response.shadeId != 255) {
    doc["shadeId"] = response.shadeId;
  }
  
  if (response.position >= 0) {
    doc["position"] = response.position;
  }
  
  if (response.tiltPosition >= 0) {
    doc["tiltPosition"] = response.tiltPosition;
  }
  
  if (response.direction != 0) {
    doc["direction"] = response.direction;
  }
  
  if (response.name.length() > 0) {
    doc["name"] = response.name;
  }
  
  if (response.remoteAddress != 0) {
    doc["remoteAddress"] = response.remoteAddress;
  }
  
  if (response.message.length() > 0) {
    doc["message"] = response.message;
  }
  
  serializeJson(doc, Serial);
  Serial.println();
}

void SerialInterface::sendError(const String& message) {
  SerialResponse response;
  response.type = "error";
  response.success = false;
  response.message = message;
  sendResponse(response);
}

void SerialInterface::sendSuccess(const String& message) {
  SerialResponse response;
  response.type = "success";
  response.success = true;
  response.message = message;
  sendResponse(response);
}

// Event callbacks
void SerialInterface::onShadeStateChanged(uint8_t shadeId, float position, float tiltPosition, int8_t direction) {
  SerialResponse response;
  response.type = "stateChanged";
  response.shadeId = shadeId;
  response.position = position;
  response.tiltPosition = tiltPosition;
  response.direction = direction;
  response.success = true;
  sendResponse(response);
}

void SerialInterface::onShadeAdded(uint8_t shadeId, const String& name, uint32_t remoteAddress) {
  SerialResponse response;
  response.type = "shadeAdded";
  response.shadeId = shadeId;
  response.name = name;
  response.remoteAddress = remoteAddress;
  response.success = true;
  sendResponse(response);
}

void SerialInterface::onShadeRemoved(uint8_t shadeId) {
  SerialResponse response;
  response.type = "shadeRemoved";
  response.shadeId = shadeId;
  response.success = true;
  sendResponse(response);
}

void SerialInterface::onCommandReceived(uint8_t shadeId, const String& command, uint32_t sourceAddress) {
  SerialResponse response;
  response.type = "commandReceived";
  response.shadeId = shadeId;
  response.message = command;
  response.remoteAddress = sourceAddress;
  response.success = true;
  sendResponse(response);
}

// Command handlers
void SerialInterface::handleMoveCommand(const SerialCommand& cmd) {
  SomfyShade* shade = controller->getShadeById(cmd.shadeId);
  if (!shade) {
    sendError("Shade not found: " + String(cmd.shadeId));
    return;
  }

  if (cmd.position >= 0 && cmd.position <= 100) {
    // Move to specific position
    shade->setTarget(cmd.position);
    if (cmd.tiltPosition >= 0 && cmd.tiltPosition <= 100) {
      shade->setTiltTarget(cmd.tiltPosition);
    }
    sendSuccess("Moving shade " + String(cmd.shadeId) + " to position " + String(cmd.position));
  } else {
    sendError("Invalid position. Must be 0-100.");
  }
}

void SerialInterface::handleStopCommand(const SerialCommand& cmd) {
  SomfyShade* shade = controller->getShadeById(cmd.shadeId);
  if (!shade) {
    sendError("Shade not found: " + String(cmd.shadeId));
    return;
  }

  shade->sendCommand(somfy_commands::My);
  sendSuccess("Stop command sent to shade " + String(cmd.shadeId));
}

void SerialInterface::handleSetMyCommand(const SerialCommand& cmd) {
  SomfyShade* shade = controller->getShadeById(cmd.shadeId);
  if (!shade) {
    sendError("Shade not found: " + String(cmd.shadeId));
    return;
  }

  if (cmd.position >= 0 && cmd.position <= 100) {
    shade->setMyPosition(cmd.position, cmd.tiltPosition >= 0 ? cmd.tiltPosition : -1);
    sendSuccess("Set MY position for shade " + String(cmd.shadeId));
  } else {
    sendError("Invalid position. Must be 0-100.");
  }
}

void SerialInterface::handleGetStatusCommand(const SerialCommand& cmd) {
  SomfyShade* shade = controller->getShadeById(cmd.shadeId);
  if (!shade) {
    sendError("Shade not found: " + String(cmd.shadeId));
    return;
  }

  SerialResponse response;
  response.type = "status";
  response.shadeId = cmd.shadeId;
  response.position = shade->getCurrentPos();
  response.tiltPosition = shade->getCurrentTiltPos();
  response.direction = shade->getDirection();
  response.name = String(shade->getName());
  response.remoteAddress = shade->getRemoteAddress();
  response.success = true;
  sendResponse(response);
}

void SerialInterface::handleListShadesCommand() {
  DynamicJsonDocument doc(2048);
  doc["type"] = "shadeList";
  doc["success"] = true;

  JsonArray shades = doc.createNestedArray("shades");

  for (uint8_t i = 0; i < SOMFY_MAX_SHADES; i++) {
    SomfyShade* shade = &controller->shades[i];
    if (shade->getShadeId() != 255) {
      JsonObject shadeObj = shades.createNestedObject();
      shadeObj["shadeId"] = shade->getShadeId();
      shadeObj["name"] = shade->getName();
      shadeObj["position"] = shade->getCurrentPos();
      shadeObj["tiltPosition"] = shade->getCurrentTiltPos();
      shadeObj["direction"] = shade->getDirection();
      shadeObj["remoteAddress"] = shade->getRemoteAddress();
    }
  }

  serializeJson(doc, Serial);
  Serial.println();
}

void SerialInterface::handleAddShadeCommand(const SerialCommand& cmd) {
  if (cmd.remoteAddress == 0) {
    sendError("Remote address required for adding shade");
    return;
  }

  SomfyShade* shade = controller->addShade();
  if (!shade) {
    sendError("Failed to add shade - maximum number reached");
    return;
  }

  shade->setRemoteAddress(cmd.remoteAddress);
  if (cmd.name.length() > 0) {
    strncpy(shade->name, cmd.name.c_str(), sizeof(shade->name) - 1);
    shade->name[sizeof(shade->name) - 1] = '\0';
  }

  controller->commit();
  sendSuccess("Added shade " + String(shade->getShadeId()) + " with address " + String(cmd.remoteAddress, HEX));
}

void SerialInterface::handleRemoveShadeCommand(const SerialCommand& cmd) {
  if (!controller->deleteShade(cmd.shadeId)) {
    sendError("Failed to remove shade " + String(cmd.shadeId));
    return;
  }

  controller->commit();
  sendSuccess("Removed shade " + String(cmd.shadeId));
}

void SerialInterface::handleSetNameCommand(const SerialCommand& cmd) {
  SomfyShade* shade = controller->getShadeById(cmd.shadeId);
  if (!shade) {
    sendError("Shade not found: " + String(cmd.shadeId));
    return;
  }

  if (cmd.name.length() == 0) {
    sendError("Name cannot be empty");
    return;
  }

  strncpy(shade->name, cmd.name.c_str(), sizeof(shade->name) - 1);
  shade->name[sizeof(shade->name) - 1] = '\0';
  controller->commit();
  sendSuccess("Set name for shade " + String(cmd.shadeId) + " to: " + cmd.name);
}

void SerialInterface::printHelp() {
  Serial.println("Somfy RTS Serial Interface Commands:");
  Serial.println();
  Serial.println("Text Commands:");
  Serial.println("  help                          - Show this help");
  Serial.println("  status                        - Show system status");
  Serial.println("  list                          - List all shades");
  Serial.println("  move <id> <pos> [tilt]        - Move shade to position (0-100)");
  Serial.println("  stop <id>                     - Stop shade movement");
  Serial.println("  getstatus <id>                - Get shade status");
  Serial.println();
  Serial.println("JSON Commands:");
  Serial.println("  {\"command\":\"move\",\"shadeId\":1,\"position\":50}");
  Serial.println("  {\"command\":\"stop\",\"shadeId\":1}");
  Serial.println("  {\"command\":\"add\",\"remoteAddress\":123456,\"name\":\"Living Room\"}");
  Serial.println("  {\"command\":\"remove\",\"shadeId\":1}");
  Serial.println("  {\"command\":\"setname\",\"shadeId\":1,\"name\":\"New Name\"}");
  Serial.println("  {\"command\":\"setmy\",\"shadeId\":1,\"position\":75}");
  Serial.println();
  Serial.println("Examples:");
  Serial.println("  move 1 50        - Move shade 1 to 50% position");
  Serial.println("  stop 1           - Stop shade 1");
  Serial.println("  list             - List all configured shades");
}

void SerialInterface::printStatus() {
  Serial.println("System Status:");
  Serial.println("  Controller: " + String(controller ? "Ready" : "Not Ready"));
  Serial.println("  Transceiver: " + String(controller && controller->transceiver.config.enabled ? "Enabled" : "Disabled"));
  Serial.println("  Frequency: " + String(controller ? controller->transceiver.config.frequency : 0.0) + " MHz");
  Serial.println("  Shade Count: " + String(controller ? controller->shadeCount() : 0));
  Serial.println("  Free Memory: " + String(ESP.getFreeHeap()) + " bytes");
}
