# Simple Somfy RTS Controller

A clean, simplified serial interface for controlling Somfy RTS motorized shades using ESP32 and CC1101 transceiver. Designed specifically for embedded applications and Raspberry Pi integration without WiFi/MQTT dependencies.

## Features

- **Serial Interface**: Clean JSON and text command API
- **Real-time Updates**: Automatic state change notifications
- **Persistent Storage**: Shade configurations saved to ESP32 flash
- **Multiple Shades**: Support for up to 16 shades
- **Position Control**: Precise position control (0-100%)
- **Movement Tracking**: Real-time position updates during movement
- **Easy Integration**: Perfect for Raspberry Pi or other serial-connected systems

## Hardware Requirements

### Components
- ESP32 development board (any variant)
- CC1101 transceiver module (433.92 MHz)
- Connecting wires
- Breadboard or PCB (optional)

### Wiring

| CC1101 Pin | ESP32 Pin | Function |
|------------|-----------|----------|
| VCC        | 3.3V      | Power supply |
| GND        | GND       | Ground |
| MOSI       | GPIO23    | SPI MOSI |
| MISO       | GPIO19    | SPI MISO |
| SCK        | GPIO18    | SPI Clock |
| CSN        | GPIO5     | Chip Select |
| GDO0       | GPIO14    | RX Data |
| GDO2       | GPIO12    | TX Data |

**Important**: Use 3.3V power supply for CC1101. Do not use 5V as it will damage the module.

## Software Requirements

### Arduino IDE Libraries
Install these libraries through the Arduino Library Manager:

1. **ELECHOUSE_CC1101_SRC_DRV** - CC1101 transceiver driver
2. **ArduinoJson** (version 6.x) - JSON parsing and generation
3. **Preferences** - ESP32 built-in library for persistent storage

### Installation Steps

1. Install Arduino IDE and ESP32 board support
2. Install required libraries
3. Copy these files to your Arduino sketch folder:
   - `SimpleSomfyController.ino`
   - `SimpleSomfyRTS.h`
   - `SimpleSomfyRTS.cpp`
   - `SimpleSerialInterface.h`
   - `SimpleSerialInterface.cpp`
4. Upload to your ESP32

## Usage

### Serial Connection
- **Baud Rate**: 115200
- **Data Bits**: 8
- **Parity**: None
- **Stop Bits**: 1
- **Flow Control**: None

### Command Formats

The controller supports both text commands and JSON format:

#### Text Commands
```
help                        - Show available commands
system                      - Show system status
list                        - List all configured shades
add <address> [name]        - Add new shade (address in hex)
remove <id>                 - Remove shade
move <id> <position>        - Move shade to position (0-100)
stop <id>                   - Stop shade movement
status <id>                 - Get shade status
setname <id> <name>         - Set shade name
setmy <id>                  - Send MY command (for programming)
reset                       - Factory reset (removes all shades)
```

#### JSON Commands
```json
{"action":"add","address":1179649,"name":"Living Room"}
{"action":"move","shadeId":0,"position":75}
{"action":"stop","shadeId":0}
{"action":"list"}
{"action":"system"}
```

### Example Session

```
> help
Available Commands:
...

> add 120001 "Living Room Shade"
{"type":"success","success":true,"timestamp":12345,"message":"Added shade 0 with address 0x120001"}

> list
{"type":"shadeList","success":true,"timestamp":12346,"shades":[{"id":0,"name":"Living Room Shade","address":1179649,"position":0,"target":0,"direction":0,"moving":false}]}

> move 0 75
{"type":"success","success":true,"timestamp":12347,"message":"Moving shade 0 to position 75"}

> {"action":"stop","shadeId":0}
{"type":"success","success":true,"timestamp":12348,"message":"Stopped shade 0"}
```

## API Reference

### Commands

#### add
Add a new shade to the system.
- **Text**: `add <address> [name]`
- **JSON**: `{"action":"add","address":1179649,"name":"Shade Name"}`
- **Parameters**:
  - `address`: Unique address in hex (e.g., 120001)
  - `name`: Optional friendly name

#### move
Move a shade to a specific position.
- **Text**: `move <id> <position>`
- **JSON**: `{"action":"move","shadeId":0,"position":75}`
- **Parameters**:
  - `id`: Shade ID (0-15)
  - `position`: Target position (0-100, where 0=closed, 100=open)

#### stop
Stop shade movement immediately.
- **Text**: `stop <id>`
- **JSON**: `{"action":"stop","shadeId":0}`

#### list
List all configured shades with their current status.
- **Text**: `list`
- **JSON**: `{"action":"list"}`

#### system
Get system status and information.
- **Text**: `system`
- **JSON**: `{"action":"system"}`

### Responses

All responses are in JSON format with these common fields:
- `type`: Response type (success, error, stateChanged, etc.)
- `success`: Boolean indicating success/failure
- `timestamp`: Milliseconds since ESP32 boot
- `message`: Human-readable message (optional)
- `data`: Additional data (optional)

### Events

The controller automatically sends events when shade states change:

```json
{
  "type": "stateChanged",
  "success": true,
  "timestamp": 12345,
  "shadeId": 0,
  "position": 45.5,
  "target": 75,
  "direction": 1,
  "moving": true
}
```

## Raspberry Pi Integration

### Python Example

```python
import serial
import json
import time

class SomfyController:
    def __init__(self, port='/dev/ttyUSB0', baudrate=115200):
        self.serial = serial.Serial(port, baudrate, timeout=1)
        time.sleep(2)  # Wait for ESP32 to initialize
    
    def send_command(self, command):
        if isinstance(command, dict):
            command = json.dumps(command)
        
        self.serial.write((command + '\n').encode())
        response = self.serial.readline().decode().strip()
        
        try:
            return json.loads(response)
        except:
            return {"error": "Invalid response", "raw": response}
    
    def add_shade(self, address, name=None):
        cmd = {"action": "add", "address": address}
        if name:
            cmd["name"] = name
        return self.send_command(cmd)
    
    def move_shade(self, shade_id, position):
        return self.send_command({
            "action": "move",
            "shadeId": shade_id,
            "position": position
        })
    
    def stop_shade(self, shade_id):
        return self.send_command({
            "action": "stop",
            "shadeId": shade_id
        })
    
    def list_shades(self):
        return self.send_command({"action": "list"})

# Usage
controller = SomfyController('/dev/ttyUSB0')

# Add a shade
result = controller.add_shade(0x120001, "Living Room")
print(result)

# Move shade to 75% open
result = controller.move_shade(0, 75)
print(result)

# List all shades
result = controller.list_shades()
print(result)
```

## Troubleshooting

### Common Issues

1. **ESP32 not responding**
   - Check serial connection and baud rate
   - Verify ESP32 is powered and running
   - Try pressing reset button on ESP32

2. **CC1101 initialization failed**
   - Verify all wiring connections
   - Ensure CC1101 is powered with 3.3V (not 5V)
   - Check for loose connections

3. **Shades not responding**
   - Verify frequency setting (433.92 MHz for most regions)
   - Check shade addresses are correct
   - Ensure shades are in pairing mode if needed

4. **JSON parsing errors**
   - Verify JSON syntax is correct
   - Check for missing quotes or commas
   - Use text commands for debugging

### Debug Mode

Enable debug output by modifying the code:
```cpp
#define DEBUG_MODE 1
```

This will provide additional serial output for troubleshooting.

## Configuration

### Frequency Settings
The default frequency is 433.92 MHz. To change for different regions:

```cpp
// In SimpleSomfyController.ino setup()
somfyController.getTransceiver().setFrequency(868.3);  // EU frequency
```

### Pin Configuration
To use different pins, modify the transceiver setup:

```cpp
// In SimpleSomfyController.ino setup()
somfyController.getTransceiver().setPins(18, 19, 23, 5, 14, 12);
//                                      SCK MISO MOSI CS GDO0 GDO2
```

## License

This project is released under the MIT License. See LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## Support

For support and questions:
1. Check this README and troubleshooting section
2. Review the example code
3. Open an issue on the project repository
