#ifndef SIMPLE_SOMFY_RTS_H
#define SIMPLE_SOMFY_RTS_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ELECHOUSE_CC1101_SRC_DRV.h>
#include <Preferences.h>

// Configuration constants
#define MAX_SHADES 16
#define SYMBOL_DURATION 640  // microseconds
#define FRAME_REPEAT_DELAY 30000  // microseconds between frame repeats

// Somfy RTS Commands
enum class SomfyCommand : uint8_t {
  MY = 0x1,
  UP = 0x2,
  MY_UP = 0x3,
  DOWN = 0x4,
  MY_DOWN = 0x5,
  UP_DOWN = 0x6,
  PROG = 0x8,
  STOP = 0x1  // Alias for MY
};

// Shade movement directions
enum class Direction : int8_t {
  STOPPED = 0,
  UP = 1,
  DOWN = -1
};

// Somfy RTS Frame structure
struct SomfyFrame {
  uint8_t key;
  uint8_t ctrl;
  uint16_t rollingCode;
  uint32_t address;
  uint8_t data[7];
  uint8_t checksum;
  bool valid;
  
  SomfyFrame() : key(0xA7), ctrl(0), rollingCode(0), address(0), checksum(0), valid(false) {
    memset(data, 0, sizeof(data));
  }
  
  void calculateChecksum();
  void encode(uint8_t* buffer);
  bool decode(uint8_t* buffer);
};

// Individual shade configuration and state
class SomfyShade {
private:
  uint8_t id;
  uint32_t address;
  uint16_t rollingCode;
  char name[32];
  float currentPosition;
  float targetPosition;
  Direction direction;
  uint32_t moveStartTime;
  uint32_t moveDuration;  // milliseconds for full travel
  bool enabled;
  
public:
  SomfyShade();
  
  // Configuration
  void setId(uint8_t shadeId);
  void setAddress(uint32_t addr);
  void setName(const char* shadeName);
  void setMoveDuration(uint32_t duration);
  void enable(bool state = true);
  
  // Getters
  uint8_t getId() const { return id; }
  uint32_t getAddress() const { return address; }
  const char* getName() const { return name; }
  float getCurrentPosition() const { return currentPosition; }
  float getTargetPosition() const { return targetPosition; }
  Direction getDirection() const { return direction; }
  bool isEnabled() const { return enabled; }
  bool isMoving() const { return direction != Direction::STOPPED; }
  
  // Movement control
  void moveToPosition(float position);
  void moveUp();
  void moveDown();
  void stop();
  void setMyPosition();
  
  // State management
  void update();  // Call regularly to update position during movement
  void setCurrentPosition(float position);
  
  // Persistence
  void save();
  void load();
  void clear();
  
  // Frame generation
  SomfyFrame createFrame(SomfyCommand command);
  uint16_t getNextRollingCode();
};

// CC1101 transceiver management
class SomfyTransceiver {
private:
  bool initialized;
  float frequency;
  int8_t txPower;
  
  // Pin configuration
  uint8_t sckPin;
  uint8_t misoPin;
  uint8_t mosiPin;
  uint8_t csPin;
  uint8_t gdo0Pin;
  uint8_t gdo2Pin;
  
public:
  SomfyTransceiver();
  
  // Configuration
  void setPins(uint8_t sck, uint8_t miso, uint8_t mosi, uint8_t cs, uint8_t gdo0, uint8_t gdo2);
  void setFrequency(float freq);
  void setTxPower(int8_t power);
  
  // Control
  bool begin();
  void end();
  bool isInitialized() const { return initialized; }
  
  // Transmission
  void sendFrame(const SomfyFrame& frame, uint8_t repeats = 3);
  void sendRawData(uint8_t* data, uint8_t length);
  
private:
  void initializeCC1101();
  void encodeManchester(uint8_t* input, uint8_t inputLen, uint8_t* output, uint8_t& outputLen);
  void sendSymbol(bool bit);
  void sendWakeup();
  void sendSync();
};

// Main controller class
class SimpleSomfyController {
private:
  SomfyShade shades[MAX_SHADES];
  SomfyTransceiver transceiver;
  Preferences preferences;
  uint32_t baseAddress;
  
public:
  SimpleSomfyController();
  
  // Initialization
  bool begin();
  void end();
  
  // Shade management
  SomfyShade* addShade(uint32_t address, const char* name = nullptr);
  SomfyShade* getShade(uint8_t id);
  SomfyShade* getShadeByAddress(uint32_t address);
  bool removeShade(uint8_t id);
  uint8_t getShadeCount();
  void listShades(JsonDocument& doc);
  
  // Control operations
  bool moveShade(uint8_t id, float position);
  bool stopShade(uint8_t id);
  bool setShadeMyPosition(uint8_t id);
  
  // System operations
  void update();  // Call regularly in main loop
  void saveConfiguration();
  void loadConfiguration();
  void factoryReset();
  
  // Transceiver access
  SomfyTransceiver& getTransceiver() { return transceiver; }
  
  // Status
  void getSystemStatus(JsonDocument& doc);
  void getShadeStatus(uint8_t id, JsonDocument& doc);
  
private:
  uint8_t findFreeSlot();
  void initializeDefaults();
};

// Global instance
extern SimpleSomfyController somfyController;

#endif // SIMPLE_SOMFY_RTS_H
