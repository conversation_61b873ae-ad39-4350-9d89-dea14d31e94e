#ifndef SOMFY_SERIAL_INTERFACE_H
#define SOMFY_SERIAL_INTERFACE_H

#include <Arduino.h>
#include <ArduinoJson.h>

// Forward declarations
class SomfyShadeController;

// Serial command structure
struct SerialCommand {
  String command;
  uint8_t shadeId;
  float position;
  float tiltPosition;
  uint32_t remoteAddress;
  String name;
  bool valid;
  
  SerialCommand() : shadeId(255), position(-1), tiltPosition(-1), remoteAddress(0), valid(false) {}
};

// Serial response structure
struct SerialResponse {
  String type;
  uint8_t shadeId;
  float position;
  float tiltPosition;
  int8_t direction;
  String name;
  uint32_t remoteAddress;
  bool success;
  String message;
  
  SerialResponse() : shadeId(255), position(-1), tiltPosition(-1), direction(0), remoteAddress(0), success(false) {}
};

class SerialInterface {
private:
  SomfyShadeController* controller;
  String inputBuffer;
  bool commandReady;
  
  // Command parsing
  void processCommand(const String& input);
  SerialCommand parseCommand(const String& input);
  SerialCommand parseTextCommand(const String& input);
  bool parseJson(const String& input, SerialCommand& cmd);
  
  // Response formatting
  void sendResponse(const SerialResponse& response);
  void sendJsonResponse(const SerialResponse& response);
  void sendError(const String& message);
  void sendSuccess(const String& message);
  
  // Command handlers
  void handleMoveCommand(const SerialCommand& cmd);
  void handleStopCommand(const SerialCommand& cmd);
  void handleSetMyCommand(const SerialCommand& cmd);
  void handleGetStatusCommand(const SerialCommand& cmd);
  void handleListShadesCommand();
  void handleAddShadeCommand(const SerialCommand& cmd);
  void handleRemoveShadeCommand(const SerialCommand& cmd);
  void handleSetNameCommand(const SerialCommand& cmd);
  
public:
  SerialInterface(SomfyShadeController* ctrl);
  
  // Main interface methods
  void begin(unsigned long baudRate = 115200);
  void loop();
  
  // Event callbacks (called by SomfyShadeController)
  void onShadeStateChanged(uint8_t shadeId, float position, float tiltPosition, int8_t direction);
  void onShadeAdded(uint8_t shadeId, const String& name, uint32_t remoteAddress);
  void onShadeRemoved(uint8_t shadeId);
  void onCommandReceived(uint8_t shadeId, const String& command, uint32_t sourceAddress);
  
  // Utility methods
  void printHelp();
  void printStatus();
};

// Global instance
extern SerialInterface serialInterface;

#endif // SOMFY_SERIAL_INTERFACE_H
