# ESPSomfy-RTS Serial Interface - Deployment Guide

## Quick Deployment Steps

### 1. Hardware Setup
Connect your CC1101 to ESP32:
```
CC1101 Pin    ESP32 Pin    Description
---------     ---------    -----------
VCC           3.3V         Power (NOT 5V!)
GND           GND          Ground
SCK           GPIO 18      SPI Clock
MOSI          GPIO 23      SPI Data Out
MISO          GPIO 19      SPI Data In
CSN           GPIO 5       Chip Select
GDO0          GPIO 14      Data Ready
GDO2          GPIO 12      Packet Received
```

### 2. Arduino IDE Setup
1. **Install ESP32 Board Support**:
   - File → Preferences
   - Add to "Additional Board Manager URLs":
     ```
     https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_dev_index.json
     ```
   - Tools → Board → Boards Manager → Search "ESP32" → Install

2. **Install Required Libraries**:
   - Tools → Manage Libraries
   - Install: **ELECHOUSE_CC1101_SRC_DRV** by L<PERSON>atan
   - Install: **Ard<PERSON><PERSON><PERSON>son** by <PERSON><PERSON> (version 6.x)

### 3. Project Files
Your project folder should contain:
```
SomfyController/
├── SomfyController.ino    (Main sketch)
├── Somfy.h               (Header file)
├── Somfy.cpp             (Core implementation)
├── SomfyStubs.cpp        (Missing function stubs)
├── SerialInterface.h     (Serial interface header)
└── SerialInterface.cpp   (Serial interface implementation)
```

### 4. Arduino IDE Configuration
- **Board**: ESP32 Dev Module
- **Upload Speed**: 921600
- **Flash Size**: 4MB (32Mb)
- **Partition Scheme**: Default 4MB with spiffs
- **Port**: Select your ESP32's COM port

### 5. Upload and Test
1. Connect ESP32 via USB
2. Click Upload (→) button
3. Open Serial Monitor (115200 baud)
4. You should see startup messages
5. Type `help` to see available commands

## Quick Test Commands

```bash
# System status
status

# Add a test shade
add shade "Test Shade"

# List shades
list shades

# Get shade info
info 1

# Listen for remotes
listen
# (Press buttons on Somfy remote, then any key to exit)

# Pair a shade (if you have a Somfy remote)
pair 1
# (Follow on-screen instructions)
```

## Troubleshooting

### Compilation Errors
- **Missing libraries**: Install ELECHOUSE_CC1101_SRC_DRV and ArduinoJson
- **Wrong ArduinoJson version**: Use version 6.x, not 7.x
- **ESP32 board not found**: Install ESP32 board support

### Upload Issues
- **Port not found**: Check USB connection and drivers
- **Permission denied**: Try different USB cable or port
- **Upload fails**: Hold BOOT button while uploading

### Runtime Issues
- **No serial output**: Check baud rate (115200)
- **System not responding**: Check wiring, especially power (3.3V)
- **CC1101 errors**: Verify all connections are secure

## File Structure Explanation

- **SomfyController.ino**: Main Arduino sketch with setup() and loop()
- **Somfy.h**: Header file with all class definitions and constants
- **Somfy.cpp**: Original Somfy implementation (modified for serial)
- **SomfyStubs.cpp**: Stub implementations for missing functions
- **SerialInterface.h/.cpp**: Complete serial command interface

## Next Steps

1. **Test with actual hardware**: Connect CC1101 and verify radio communication
2. **Pair real shades**: Use `pair` command with actual Somfy remotes
3. **Test shade control**: Try movement commands once paired
4. **Customize settings**: Use `set` commands to adjust timing and properties

## Support

If you encounter issues:
1. Check wiring connections (especially 3.3V power)
2. Verify all libraries are installed correctly
3. Use `status` command to check system state
4. Try `listen` mode to verify radio reception

The system should now be ready to control your Somfy RTS shades via serial commands!
