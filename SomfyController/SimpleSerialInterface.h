#ifndef SIMPLE_SERIAL_INTERFACE_H
#define SIMPLE_SERIAL_INTERFACE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include "SimpleSomfyRTS.h"

// Command structure for parsing
struct Command {
  String action;
  uint8_t shadeId;
  float position;
  uint32_t address;
  String name;
  bool valid;
  
  Command() : shadeId(255), position(-1), address(0), valid(false) {}
};

// Response structure
struct Response {
  String type;
  bool success;
  String message;
  JsonDocument data;
  
  Response() : success(false) {}
};

class SimpleSerialInterface {
private:
  String inputBuffer;
  bool commandReady;
  unsigned long lastHeartbeat;
  
  // Command parsing
  Command parseCommand(const String& input);
  Command parseTextCommand(const String& input);
  Command parseJsonCommand(const String& input);
  
  // Response handling
  void sendResponse(const Response& response);
  void sendError(const String& message);
  void sendSuccess(const String& message, JsonDocument* data = nullptr);
  void sendJson(JsonDocument& doc);
  
  // Command handlers
  void handleListCommand();
  void handleStatusCommand();
  void handleMoveCommand(const Command& cmd);
  void handleStopCommand(const Command& cmd);
  void handleAddCommand(const Command& cmd);
  void handleRemoveCommand(const Command& cmd);
  void handleSetNameCommand(const Command& cmd);
  void handleSetMyCommand(const Command& cmd);
  void handleSystemCommand();
  void handleHelpCommand();
  void handleResetCommand();
  
  // Utility
  void processCommand(const String& input);
  
public:
  SimpleSerialInterface();
  
  // Main interface
  void begin(unsigned long baudRate = 115200);
  void loop();
  
  // Event notifications (called by controller when state changes)
  void notifyShadeStateChanged(uint8_t shadeId);
  void notifyShadeAdded(uint8_t shadeId);
  void notifyShadeRemoved(uint8_t shadeId);
  
  // Utility
  void printWelcome();
  void printHelp();
  void sendHeartbeat();
};

// Global instance
extern SimpleSerialInterface serialInterface;

#endif // SIMPLE_SERIAL_INTERFACE_H
