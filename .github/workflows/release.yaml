name: ESPSomfy-RTS Release

on: 
  release:
    types: [published]

env:
  ARDUIN<PERSON>_BOARD_MANAGER_ADDITIONAL_URLS: "https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json"
  ARDUINO_CLI_VERSION: "0.x"
  ARDUINO_ESP32_VERSION: "2.0.17"
  ARDUINO_JSON_VERSION: "6.21.5"
  ESPTOOL_VERSION: "4.7"
  LITTLEFS_VERSION: "v2.5.1"
  MKLITTLEFS_VERSION: "3.1.0"
  PUB_SUB_CLIENT_VERSION: "2.8.0"
  PYTHON_VERSION: "3.10"
  SMARTRC_CC1101_VERSION: "2.5.7"
  WEB_SOCKET_VERSION: "2.4.0"

jobs:
  littlefs:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest

    steps:
      - name: Get Release
        id: get_release
        uses: bruceadams/get-release@v1.3.2
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - name: Check out code
        uses: actions/checkout@v4

      - name: Checkout mklittlefs
        uses: actions/checkout@v4
        with:
          repository: earlephilhower/mklittlefs
          path: mklittlefs
          ref: ${{ env.MKLITTLEFS_VERSION }}

      - name: Checkout LittleFS
        uses: actions/checkout@v4
        with:
          repository: littlefs-project/littlefs
          path: mklittlefs/littlefs
          ref: ${{ env.LITTLEFS_VERSION }}

      - name: Build mklittlefs
        run: |
          make -C mklittlefs

      - name: Create LittleFS
        run: |
          ./mklittlefs/mklittlefs --create data --size 1441792 SomfyController.littlefs.bin

      - name: Upload binaries
        uses: actions/upload-artifact@v4
        with:
          name: LittleFS
          path: SomfyController.littlefs.bin
          retention-days: 5

      - name: Upload LittleFS
        uses: shogo82148/actions-upload-release-asset@v1.7.5
        with:
          github_token: ${{ github.token }}
          upload_url: ${{ steps.get_release.outputs.upload_url }}
          asset_name: SomfyController.littlefs.bin
          asset_path: SomfyController.littlefs.bin
          overwrite: true

  arduino:
    permissions: write-all
    name: ${{ matrix.name }}
    needs: [littlefs]
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        include:
          - board: esp32
            addr_bootloader: 0x1000
            chip: ESP32
            fqbn: esp32:esp32:esp32wrover:PartitionScheme=default,FlashMode=qio,FlashFreq=80,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            #     esp32:esp32:esp32wrover:PartitionScheme=default,FlashMode=qio,FlashFreq=80,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            name: ESP32
            obname: SomfyController.onboard.esp32.bin
            fwname: SomfyController.ino.esp32.bin
          - board: esp32c3
            addr_bootloader: 0x0
            chip: ESP32-C3
            fqbn: esp32:esp32:esp32c3:JTAGAdapter=default,CDCOnBoot=cdc,PartitionScheme=default,CPUFreq=160,FlashMode=qio,FlashFreq=80,FlashSize=4M,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            #     esp32:esp32:esp32c3:JTAGAdapter=default,CDCOnBoot=default,PartitionScheme=default,CPUFreq=160,FlashMode=qio,FlashFreq=80,FlashSize=4M,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            name: ESP32C3
            obname: SomfyController.onboard.esp32c3.bin
            fwname: SomfyController.ino.esp32c3.bin
          - board: esp32s2
            addr_bootloader: 0x1000
            chip: ESP32-S2
            fqbn: esp32:esp32:esp32s2:JTAGAdapter=default,CDCOnBoot=cdc,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,PSRAM=disabled,PartitionScheme=default,CPUFreq=240,FlashMode=qio,FlashFreq=80,FlashSize=4M,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            #     esp32:esp32:esp32s2:JTAGAdapter=default,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,PSRAM=disabled,PartitionScheme=default,CPUFreq=240,FlashMode=qio,FlashFreq=80,FlashSize=4M,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            name: ESP32S2
            obname: SomfyController.onboard.esp32s2.bin
            fwname: SomfyController.ino.esp32s2.bin
          - board: esp32s3
            addr_bootloader: 0x0
            chip: ESP32-S3
            fqbn: esp32:esp32:esp32s3:JTAGAdapter=default,PSRAM=disabled,FlashMode=qio,FlashSize=4M,LoopCore=1,EventsCore=1,USBMode=hwcdc,CDCOnBoot=cdc,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,PartitionScheme=default,CPUFreq=240,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            #     esp32:esp32:esp32s3:JTAGAdapter=default,PSRAM=disabled,FlashMode=qio,FlashSize=4M,LoopCore=1,EventsCore=1,USBMode=hwcdc,CDCOnBoot=cdc,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,PartitionScheme=default,CPUFreq=240,UploadSpeed=921600,DebugLevel=none,EraseFlash=none
            name: ESP32S3
            fwname: SomfyController.ino.esp32s3.bin
            obname: SomfyController.onboard.esp32s3.bin
    steps:
      - name: Get Release
        id: get_release
        uses: bruceadams/get-release@v1.3.2
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - name: Check out code
        uses: actions/checkout@v4
        with:
          path: SomfyController

      - name: Get LittleFS
        uses: actions/download-artifact@v4
        with:
          name: LittleFS

      - name: Install Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Upgrade pip
        run: |
          python -m pip install --upgrade pip
          pip --version

      - name: Install ESPTool
        run: |
          pip install esptool==${{ env.ESPTOOL_VERSION }}

      - name: Install Arduino CLI
        uses: arduino/setup-arduino-cli@v1
        with:
          version: ${{ env.ARDUINO_CLI_VERSION }}

      - name: Configure Arduino CLI
        run: |
          arduino-cli core update-index
          arduino-cli core install esp32:esp32@${{ env.ARDUINO_ESP32_VERSION }}

      - name: Configure Arduino Libraries
        run: |
          arduino-cli lib install ArduinoJson@${{ env.ARDUINO_JSON_VERSION }}
          arduino-cli lib install PubSubClient@${{ env.PUB_SUB_CLIENT_VERSION }}
          arduino-cli lib install SmartRC-CC1101-Driver-Lib@${{ env.SMARTRC_CC1101_VERSION }}
          arduino-cli lib install WebSockets@${{ env.WEB_SOCKET_VERSION }}

      - name: Build ${{ matrix.name }}
        run: |
          mkdir -p build${{ matrix.name }}
          arduino-cli compile --clean --output-dir build${{ matrix.name }} --fqbn ${{ matrix.fqbn }} --warnings none ./SomfyController

      - name: ${{ matrix.name }} Image
        run: |
          python -m esptool --chip ${{ matrix.chip }} \
            merge_bin -o ${{ matrix.obname }} \
            ${{ matrix.addr_bootloader }} build${{ matrix.name }}/SomfyController.ino.bootloader.bin \
            0x8000 build${{ matrix.name }}/SomfyController.ino.partitions.bin \
            0x10000 build${{ matrix.name }}/SomfyController.ino.bin \
            0x290000 SomfyController.littlefs.bin

      - name: Upload Firmware ${{ matrix.name }}
        uses: shogo82148/actions-upload-release-asset@v1.7.5
        with:
          github_token: ${{ github.token }}
          upload_url: ${{ steps.get_release.outputs.upload_url }}
          asset_name: ${{ matrix.fwname }}
          asset_path: build${{ matrix.name }}/SomfyController.ino.bin

      - name: ${{ matrix.name }} Compress Onboard Image
        run: |
          zip ${{ matrix.obname }}.zip ./${{ matrix.obname }}

      - name: Upload Onboard ${{ matrix.name }}
        uses: shogo82148/actions-upload-release-asset@v1.7.5
        # env:
        #  GITHUB_TOKEN: ${{ github.token }}
        with:
          github_token: ${{ github.token }}
          upload_url: ${{ steps.get_release.outputs.upload_url }}
          asset_name: ${{ matrix.obname }}.zip
          asset_path: ${{ matrix.obname }}.zip
          overwrite: true
          asset_content_type: application/zip
          
