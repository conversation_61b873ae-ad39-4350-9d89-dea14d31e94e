/*
 * DEAD SIMPLE Somfy RTS Controller
 *
 * Just:
 * 1. Listen for remotes
 * 2. Send commands
 * 3. Serial interface
 *
 * Commands:
 * listen - start listening
 * send 123456 2 - send UP to address 123456
 */

// #include <ELECHOUSE_CC1101_SRC_DRV.h>  // Commented out to test basic functionality

void setup() {
  Serial.begin(115200);
  delay(2000);  // Longer delay for serial to stabilize

  Serial.println("=== BASIC SOMFY CONTROLLER ===");
  Serial.println("Starting initialization...");

  // CC1101 initialization disabled for testing
  Serial.println("CC1101 library disabled for testing...");
  Serial.println("This version will just echo commands without actual RF transmission");
  Serial.println("Check wiring if you want to enable CC1101:");
  Serial.println("  VCC -> 3.3V");
  Serial.println("  GND -> GND");
  Serial.println("  CSN -> GPIO5");
  Serial.println("  SCK -> GPIO18");
  Serial.println("  MOSI -> GPIO23");
  Serial.println("  MISO -> GPIO19");
  Serial.println("  GDO0 -> GPIO14");
  Serial.println("  GDO2 -> GPIO12");

  Serial.println("Commands:");
  Serial.println("  listen - start listening for remotes");
  Serial.println("  send <address> <cmd> - send command (1=STOP, 2=UP, 4=DOWN)");
  Serial.println("Ready!");
  Serial.print("> ");
}

void loop() {
  if (Serial.available()) {
    String cmd = Serial.readStringUntil('\n');
    cmd.trim();
    
    if (cmd == "listen") {
      Serial.println("Listening... (press any key to stop)");
      listenForRemotes();
    } else if (cmd.startsWith("send ")) {
      // Parse: send 123456 2
      int space = cmd.indexOf(' ', 5);
      if (space > 0) {
        String addr = cmd.substring(5, space);
        String command = cmd.substring(space + 1);
        
        uint32_t address = strtoul(addr.c_str(), nullptr, 16);
        uint8_t cmd_byte = command.toInt();
        
        sendCommand(address, cmd_byte);
      }
    } else if (cmd.length() > 0) {
      Serial.println("Commands: listen, send <addr> <cmd>");
    }
    
    Serial.print("> ");
  }
}

void listenForRemotes() {
  Serial.println("SIMULATE: Would start listening for remotes...");
  Serial.println("CC1101 library disabled - this is just a test");
  Serial.println("Press any key to stop simulation");

  int count = 0;
  while (!Serial.available()) {
    count++;
    if (count % 100 == 0) {
      Serial.printf("SIMULATE: Listening... (%d seconds)\n", count/100);
    }

    // Simulate receiving some data every 5 seconds
    if (count % 500 == 0) {
      Serial.println("SIMULATE: Received 7 bytes: A7 02 45 12 34 56 78");
    }

    delay(10);
  }

  // Clear serial buffer
  while (Serial.available()) Serial.read();

  Serial.println("SIMULATE: Stopped listening.");
}

void sendCommand(uint32_t address, uint8_t command) {
  Serial.printf("SIMULATE: Sending command %d to 0x%06X\n", command, address);

  // Basic Somfy frame (8 bytes, not 7)
  byte frame[8];
  frame[0] = 0xA7;                    // Key
  frame[1] = command;                 // Command
  frame[2] = 0x00;                    // Checksum (calculate later)
  frame[3] = 0x00;                    // Rolling code high
  frame[4] = 0x01;                    // Rolling code low
  frame[5] = (address >> 16) & 0xFF;  // Address high
  frame[6] = (address >> 8) & 0xFF;   // Address mid
  frame[7] = address & 0xFF;          // Address low

  // Calculate checksum
  frame[2] = 0;
  for (int i = 0; i < 8; i++) {
    if (i != 2) frame[2] ^= frame[i];
  }

  Serial.print("SIMULATE: Frame would be: ");
  for (int i = 0; i < 8; i++) {
    Serial.printf("%02X ", frame[i]);
  }
  Serial.println();

  Serial.println("SIMULATE: Would transmit via CC1101 (disabled)");
  Serial.println("SIMULATE: Command sent!");
}
