/*
 * Simple Somfy RTS Controller
 * 
 * A clean, simplified serial interface for controlling Somfy RTS shades
 * without WiFi/MQTT dependencies. Perfect for Raspberry Pi integration.
 * 
 * Hardware Requirements:
 * - ESP32 development board
 * - CC1101 transceiver module
 * 
 * Default Wiring:
 * CC1101    ESP32
 * VCC   ->  3.3V
 * GND   ->  GND
 * MOSI  ->  GPIO23
 * MISO  ->  GPIO19
 * SCK   ->  GPIO18
 * CSN   ->  GPIO5
 * GDO0  ->  GPIO14
 * GDO2  ->  GPIO12
 * 
 * Serial Interface:
 * - Baud Rate: 115200
 * - Format: JSON and text commands
 * - Real-time status updates
 * 
 * Author: AI Assistant
 * Version: 1.0
 * License: MIT
 */

#include <Arduino.h>
#include <esp_task_wdt.h>
#include "SimpleSomfyRTS.h"
#include "SimpleSerialInterface.h"

// Global instances (defined in respective .cpp files)
extern SimpleSomfyController somfyController;
extern SimpleSerialInterface serialInterface;

// Configuration
const unsigned long SERIAL_BAUD_RATE = 115200;
const unsigned long WATCHDOG_TIMEOUT = 10000;  // 10 seconds
const unsigned long LOOP_DELAY = 10;           // 10ms loop delay

// State tracking for notifications
float lastPositions[MAX_SHADES];
Direction lastDirections[MAX_SHADES];

void setup() {
  // Initialize watchdog timer
  esp_task_wdt_config_t wdt_config = {
    .timeout_ms = WATCHDOG_TIMEOUT,
    .idle_core_mask = (1 << portNUM_PROCESSORS) - 1,
    .trigger_panic = false  // Don't panic, just reset
  };
  esp_task_wdt_init(&wdt_config);
  esp_task_wdt_add(NULL);
  
  // Initialize serial interface first
  serialInterface.begin(SERIAL_BAUD_RATE);
  
  // Initialize Somfy controller
  Serial.println("Initializing Somfy RTS controller...");
  
  if (!somfyController.begin()) {
    Serial.println("ERROR: Failed to initialize Somfy controller!");
    Serial.println("Please check:");
    Serial.println("- CC1101 module wiring");
    Serial.println("- Power supply (3.3V)");
    Serial.println("- SPI connections");
    
    while (true) {
      delay(1000);
      esp_task_wdt_reset();
    }
  }
  
  Serial.println("Somfy RTS controller initialized successfully!");
  Serial.println("CC1101 transceiver ready on 433.92 MHz");
  Serial.println();
  
  // Initialize state tracking
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    lastPositions[i] = -1;
    lastDirections[i] = Direction::STOPPED;
  }
  
  // Load existing configuration
  Serial.print("Loaded ");
  Serial.print(somfyController.getShadeCount());
  Serial.println(" shades from configuration");
  
  Serial.println("Ready for commands!");
  Serial.print("> ");
}

void loop() {
  // Reset watchdog timer
  esp_task_wdt_reset();
  
  // Process serial interface
  serialInterface.loop();
  
  // Update Somfy controller (handles shade movements)
  somfyController.update();
  
  // Check for state changes and send notifications
  checkForStateChanges();
  
  // Small delay to prevent excessive CPU usage
  delay(LOOP_DELAY);
}

void checkForStateChanges() {
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    SomfyShade* shade = somfyController.getShade(i);
    if (!shade) continue;
    
    float currentPos = shade->getCurrentPosition();
    Direction currentDir = shade->getDirection();
    
    // Check if position or direction changed significantly
    bool positionChanged = abs(currentPos - lastPositions[i]) > 1.0f;
    bool directionChanged = currentDir != lastDirections[i];
    
    if (positionChanged || directionChanged) {
      // Send state change notification
      serialInterface.notifyShadeStateChanged(i);
      
      // Update tracking
      lastPositions[i] = currentPos;
      lastDirections[i] = currentDir;
    }
  }
}

// Optional: Handle system errors
void handleError(const String& error) {
  Serial.println("SYSTEM ERROR: " + error);
  
  // Try to recover
  delay(1000);
  esp_task_wdt_reset();
}

// Optional: System information function
void printSystemInfo() {
  Serial.println("=== System Information ===");
  Serial.println("Chip Model: " + String(ESP.getChipModel()));
  Serial.println("Chip Revision: " + String(ESP.getChipRevision()));
  Serial.println("CPU Frequency: " + String(ESP.getCpuFreqMHz()) + " MHz");
  Serial.println("Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
  Serial.println("Flash Size: " + String(ESP.getFlashChipSize()) + " bytes");
  Serial.println("Uptime: " + String(millis() / 1000) + " seconds");
  Serial.println("===========================");
}
