/*
 * Minimal Somfy RTS Controller
 * 
 * ONLY does:
 * 1. Listen for remote commands (receive RF)
 * 2. Send commands to shades (transmit RF)
 * 3. Serial interface for both
 * 
 * Hardware: ESP32 + CC1101
 * Serial: 115200 baud
 */

#include <Arduino.h>
#include <ELECHOUSE_CC1101_SRC_DRV.h>

// CC1101 pins (ESP32 default)
#define CC1101_GDO0  14  // RX pin
#define CC1101_GDO2  12  // TX pin

// Somfy frame structure (7 bytes)
struct SomfyFrame {
  uint8_t key;          // Always 0xA7
  uint8_t ctrl;         // Command (1=MY/STOP, 2=UP, 4=DOWN, 8=PROG)
  uint8_t checksum;     // XOR of all other bytes
  uint16_t rolling;     // Rolling code
  uint32_t address;     // Remote address (24-bit)
  
  void print() {
    Serial.printf("Key:0x%02X Cmd:0x%02X Chk:0x%02X Roll:%d Addr:0x%06X\n", 
                  key, ctrl, checksum, rolling, address);
  }
  
  void calculateChecksum() {
    checksum = key ^ ctrl ^ (rolling >> 8) ^ (rolling & 0xFF) ^ 
               (address >> 16) ^ (address >> 8) ^ (address & 0xFF);
  }
};

// Global variables
volatile bool frameReceived = false;
volatile uint32_t pulseBuffer[200];
volatile uint16_t pulseCount = 0;
volatile uint32_t lastPulseTime = 0;
SomfyFrame lastFrame;
uint16_t myRollingCode = 1;

// Function declarations
void IRAM_ATTR pulseISR();
void setupCC1101();
void handleCommand(String cmd);
void startListening();
void stopListening();
bool decodeSomfyFrame();
void sendSomfyCommand(uint32_t address, uint8_t command);
void sendBit(uint8_t bit);

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("=== Minimal Somfy RTS Controller ===");
  Serial.println("Commands:");
  Serial.println("  listen          - Start listening for remotes");
  Serial.println("  stop_listen     - Stop listening");
  Serial.println("  send <addr> <cmd> - Send command (addr in hex, cmd: 1=STOP, 2=UP, 4=DOWN)");
  Serial.println("  Example: send 123456 2  (sends UP to address 0x123456)");
  Serial.println();
  
  // Initialize CC1101
  ELECHOUSE_cc1101.Init();
  if (!ELECHOUSE_cc1101.getCC1101()) {
    Serial.println("ERROR: CC1101 not found!");
    while(1) delay(1000);
  }
  
  // Configure for Somfy RTS
  setupCC1101();
  
  Serial.println("CC1101 initialized. Ready!");
  Serial.print("> ");
}

void loop() {
  // Handle serial commands
  if (Serial.available()) {
    String cmd = Serial.readStringUntil('\n');
    cmd.trim();
    handleCommand(cmd);
    Serial.print("> ");
  }
  
  // Handle received frames
  if (frameReceived) {
    frameReceived = false;
    if (decodeSomfyFrame()) {
      Serial.println("RECEIVED:");
      lastFrame.print();
    }
  }
  
  delay(10);
}

void setupCC1101() {
  ELECHOUSE_cc1101.setCCMode(1);          // Internal mode
  ELECHOUSE_cc1101.setModulation(0);      // 2-FSK
  ELECHOUSE_cc1101.setMHZ(433.92);        // Frequency
  ELECHOUSE_cc1101.setDeviation(47.60);   // Deviation
  ELECHOUSE_cc1101.setChannel(0);
  ELECHOUSE_cc1101.setChsp(199.95);
  ELECHOUSE_cc1101.setRxBW(325.0);
  ELECHOUSE_cc1101.setDRate(3.79372);
  ELECHOUSE_cc1101.setPA(10);             // TX power
  ELECHOUSE_cc1101.setSyncMode(0);        // No sync word for raw mode
  ELECHOUSE_cc1101.setPktFormat(3);       // Async serial mode
  ELECHOUSE_cc1101.setLengthConfig(0);    // Fixed length
  ELECHOUSE_cc1101.setPacketLength(0);
  ELECHOUSE_cc1101.setCrc(0);             // No CRC
  ELECHOUSE_cc1101.setDcFilterOff(0);
  ELECHOUSE_cc1101.setManchester(0);      // No Manchester
  
  pinMode(CC1101_GDO0, INPUT);
  pinMode(CC1101_GDO2, OUTPUT);
}

void handleCommand(String cmd) {
  cmd.toLowerCase();
  
  if (cmd == "listen") {
    startListening();
  } else if (cmd == "stop_listen") {
    stopListening();
  } else if (cmd.startsWith("send ")) {
    // Parse: send <address> <command>
    int space1 = cmd.indexOf(' ', 5);
    if (space1 > 0) {
      String addrStr = cmd.substring(5, space1);
      String cmdStr = cmd.substring(space1 + 1);
      
      uint32_t address = strtoul(addrStr.c_str(), nullptr, 16);
      uint8_t command = cmdStr.toInt();
      
      if (address > 0 && command > 0) {
        sendSomfyCommand(address, command);
      } else {
        Serial.println("Invalid address or command");
      }
    } else {
      Serial.println("Usage: send <address_hex> <command>");
    }
  } else if (cmd == "help") {
    Serial.println("Commands: listen, stop_listen, send <addr> <cmd>");
  } else if (cmd.length() > 0) {
    Serial.println("Unknown command. Type 'help' for usage.");
  }
}

void startListening() {
  Serial.println("Starting to listen for Somfy remotes...");
  pulseCount = 0;
  frameReceived = false;
  
  // Set CC1101 to RX mode
  ELECHOUSE_cc1101.SetRx();
  
  // Attach interrupt to GDO0 for pulse detection
  attachInterrupt(digitalPinToInterrupt(CC1101_GDO0), pulseISR, CHANGE);
}

void stopListening() {
  Serial.println("Stopped listening.");
  detachInterrupt(digitalPinToInterrupt(CC1101_GDO0));
  ELECHOUSE_cc1101.setSidle();
}

void IRAM_ATTR pulseISR() {
  uint32_t now = micros();
  uint32_t duration = now - lastPulseTime;
  lastPulseTime = now;
  
  // Store pulse duration
  if (pulseCount < 199) {
    pulseBuffer[pulseCount++] = duration;
  }
  
  // Check for end of frame (long silence)
  if (duration > 10000 && pulseCount > 50) {
    frameReceived = true;
  }
}

bool decodeSomfyFrame() {
  if (pulseCount < 100) return false;
  
  // Look for Somfy sync pattern and decode
  // This is a simplified decoder - real Somfy uses Manchester encoding
  
  uint8_t bits[56];  // 7 bytes = 56 bits
  uint8_t bitCount = 0;
  
  // Find sync pattern (looking for specific pulse patterns)
  for (uint16_t i = 10; i < pulseCount - 56 && bitCount < 56; i++) {
    uint32_t pulse = pulseBuffer[i];
    
    // Decode pulse to bit (simplified)
    if (pulse > 400 && pulse < 800) {
      bits[bitCount++] = (pulse > 600) ? 1 : 0;
    }
  }
  
  if (bitCount < 56) return false;
  
  // Convert bits to frame
  lastFrame.key = 0;
  lastFrame.ctrl = 0;
  lastFrame.checksum = 0;
  lastFrame.rolling = 0;
  lastFrame.address = 0;
  
  for (int i = 0; i < 8; i++) lastFrame.key |= (bits[i] << (7-i));
  for (int i = 0; i < 8; i++) lastFrame.ctrl |= (bits[8+i] << (7-i));
  for (int i = 0; i < 8; i++) lastFrame.checksum |= (bits[16+i] << (7-i));
  for (int i = 0; i < 16; i++) lastFrame.rolling |= (bits[24+i] << (15-i));
  for (int i = 0; i < 24; i++) lastFrame.address |= (bits[32+i] << (23-i));
  
  // Reset for next frame
  pulseCount = 0;
  
  return (lastFrame.key == 0xA7);  // Valid Somfy frame starts with 0xA7
}

void sendSomfyCommand(uint32_t address, uint8_t command) {
  Serial.printf("Sending command %d to address 0x%06X\n", command, address);
  
  // Create frame
  SomfyFrame frame;
  frame.key = 0xA7;
  frame.ctrl = command;
  frame.rolling = myRollingCode++;
  frame.address = address;
  frame.calculateChecksum();
  
  // Convert to bytes
  uint8_t data[8];
  data[0] = frame.key;
  data[1] = frame.ctrl;
  data[2] = frame.checksum;
  data[3] = (frame.rolling >> 8) & 0xFF;
  data[4] = frame.rolling & 0xFF;
  data[5] = (frame.address >> 16) & 0xFF;
  data[6] = (frame.address >> 8) & 0xFF;
  data[7] = frame.address & 0xFF;
  
  // Send frame
  stopListening();  // Stop RX mode
  
  ELECHOUSE_cc1101.SetTx();  // TX mode
  
  // Send wakeup
  digitalWrite(CC1101_GDO2, HIGH);
  delayMicroseconds(9415);
  digitalWrite(CC1101_GDO2, LOW);
  delayMicroseconds(89565);
  
  // Send sync
  for (int i = 0; i < 2; i++) {
    sendBit(1);
    sendBit(0);
  }
  sendBit(1);
  delayMicroseconds(4550);
  
  // Send data (simplified Manchester encoding)
  for (int i = 0; i < 8; i++) {
    for (int bit = 7; bit >= 0; bit--) {
      sendBit((data[i] >> bit) & 1);
    }
  }
  
  ELECHOUSE_cc1101.setSidle();  // Back to idle
  
  Serial.println("Command sent!");
}

void sendBit(uint8_t bit) {
  if (bit) {
    // Send '1': high then low
    digitalWrite(CC1101_GDO2, HIGH);
    delayMicroseconds(640);
    digitalWrite(CC1101_GDO2, LOW);
    delayMicroseconds(640);
  } else {
    // Send '0': low then high  
    digitalWrite(CC1101_GDO2, LOW);
    delayMicroseconds(640);
    digitalWrite(CC1101_GDO2, HIGH);
    delayMicroseconds(640);
  }
}
