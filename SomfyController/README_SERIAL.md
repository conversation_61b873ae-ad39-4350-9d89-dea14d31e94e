# Somfy RTS Serial Interface

A simplified serial interface for controlling Somfy RTS shades using ESP32 and CC1101 transceiver, designed for embedded applications without WiFi/MQTT dependencies.

## Hardware Requirements

- ESP32 development board
- CC1101 transceiver module
- Connecting wires

## Wiring

Connect the CC1101 module to your ESP32 as follows:

| CC1101 Pin | ESP32 Pin | Description |
|------------|-----------|-------------|
| VCC        | 3.3V      | Power supply |
| GND        | GND       | Ground |
| MOSI       | GPIO23    | SPI MOSI |
| MISO       | GPIO19    | SPI MISO |
| SCK        | GPIO18    | SPI Clock |
| CSN        | GPIO5     | Chip Select |
| GDO0       | GPIO14    | RX Data |
| GDO2       | GPIO12    | TX Data |

## Installation

1. Install the required libraries in Arduino IDE:
   - SmartRC-CC1101-Driver-Lib
   - ArduinoJson

2. Copy the following files to your Arduino sketch folder:
   - `SomfySerialController.ino` (main sketch)
   - `SomfySerialInterface.h`
   - `SomfySerialInterface.cpp`
   - `SomfyMinimal.cpp`
   - `Somfy.h`

3. Upload the sketch to your ESP32

## Usage

### Serial Communication

Connect to the ESP32 via serial at 115200 baud. The interface supports both text commands and JSON format.

### Text Commands

```
help                          - Show available commands
status                        - Show system status
list                          - List all configured shades
move <id> <pos> [tilt]        - Move shade to position (0-100%)
stop <id>                     - Stop shade movement
getstatus <id>                - Get current shade status
```

### JSON Commands

```json
{"command":"move","shadeId":1,"position":50}
{"command":"stop","shadeId":1}
{"command":"add","remoteAddress":123456,"name":"Living Room"}
{"command":"remove","shadeId":1}
{"command":"setname","shadeId":1,"name":"New Name"}
{"command":"setmy","shadeId":1,"position":75}
```

### Examples

```
> help
Somfy RTS Serial Interface Commands:
...

> move 1 50
{"type":"success","success":true,"message":"Moving shade 1 to position 50"}

> list
{"type":"shadeList","success":true,"shades":[{"shadeId":1,"name":"Living Room","position":50,"tiltPosition":0,"direction":0,"remoteAddress":1179649}]}

> {"command":"add","remoteAddress":123456,"name":"Bedroom"}
{"type":"success","success":true,"message":"Added shade 2 with address 1E240"}
```

## API Reference

### Commands

#### move
Move a shade to a specific position.
- **Text**: `move <shadeId> <position> [tiltPosition]`
- **JSON**: `{"command":"move","shadeId":1,"position":50,"tiltPosition":25}`

#### stop
Stop shade movement immediately.
- **Text**: `stop <shadeId>`
- **JSON**: `{"command":"stop","shadeId":1}`

#### add
Add a new shade with specified remote address.
- **JSON**: `{"command":"add","remoteAddress":123456,"name":"Shade Name"}`

#### remove
Remove a shade from the system.
- **JSON**: `{"command":"remove","shadeId":1}`

#### setname
Set or change a shade's name.
- **JSON**: `{"command":"setname","shadeId":1,"name":"New Name"}`

#### setmy
Set the MY position for a shade.
- **JSON**: `{"command":"setmy","shadeId":1,"position":75}`

#### list
List all configured shades.
- **Text**: `list`
- **JSON**: `{"command":"list"}`

#### getstatus
Get current status of a specific shade.
- **Text**: `getstatus <shadeId>`
- **JSON**: `{"command":"getstatus","shadeId":1}`

### Responses

All responses are in JSON format:

```json
{
  "type": "success|error|status|stateChanged|shadeList",
  "success": true|false,
  "shadeId": 1,
  "position": 50.0,
  "tiltPosition": 25.0,
  "direction": 0,
  "name": "Shade Name",
  "remoteAddress": 123456,
  "message": "Status message"
}
```

### Events

The interface automatically sends events when shade states change:

```json
{"type":"stateChanged","shadeId":1,"position":75,"tiltPosition":0,"direction":1,"success":true}
```

## Integration

This serial interface is designed for integration with:
- Raspberry Pi projects
- Home automation systems
- Custom control applications
- Microcontroller-based projects

The simple JSON protocol makes it easy to integrate with any system that can communicate over serial.

## Troubleshooting

1. **No response from ESP32**: Check serial connection and baud rate (115200)
2. **CC1101 initialization failed**: Verify wiring connections
3. **Commands not working**: Ensure JSON format is valid
4. **Shades not responding**: Check frequency settings and remote addresses

## Frequency Configuration

The default frequency is 433.92 MHz. This can be modified in the transceiver configuration if needed for your region.

## License

This project is based on the ESPSomfy-RTS project and maintains the same licensing terms.
