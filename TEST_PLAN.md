# ESPSomfy-RTS Serial Interface Test Plan

## Pre-Test Setup

### Hardware Requirements
- [ ] ESP32 development board
- [ ] CC1101 radio transceiver module
- [ ] Somfy RTS remote control
- [ ] Somfy RTS motorized shade/blind for testing
- [ ] USB cable for serial connection
- [ ] Breadboard and jumper wires

### Software Requirements
- [ ] Arduino IDE with ESP32 support
- [ ] Serial terminal (Arduino IDE Serial Monitor, PuTTY, etc.)
- [ ] Required libraries installed

### Wiring Verification
- [ ] CC1101 VCC to ESP32 3.3V
- [ ] CC1101 GND to ESP32 GND
- [ ] CC1101 SCK to ESP32 GPIO 18
- [ ] CC1101 MOSI to ESP32 GPIO 23
- [ ] CC1101 MISO to ESP32 GPIO 19
- [ ] CC1101 CSN to ESP32 GPIO 5
- [ ] CC1101 GDO0 to ESP32 GPIO 14
- [ ] CC1101 GDO2 to ESP32 GPIO 12

## Test Cases

### 1. System Initialization Tests

#### Test 1.1: Basic Startup
- [ ] Upload firmware to ESP32
- [ ] Connect serial terminal at 115200 baud
- [ ] Verify startup messages appear
- [ ] Verify prompt ">" appears
- [ ] Expected: Clean startup with no errors

#### Test 1.2: Help Command
- [ ] Type `help` and press Enter
- [ ] Verify all commands are listed
- [ ] Verify command descriptions are clear
- [ ] Expected: Complete help text displayed

#### Test 1.3: Status Command
- [ ] Type `status` and press Enter
- [ ] Verify system information is displayed
- [ ] Check transceiver configuration
- [ ] Expected: Status shows transceiver enabled at 433.92 MHz

### 2. Shade Management Tests

#### Test 2.1: Add Shade
- [ ] Type `add shade "Test Shade"` and press Enter
- [ ] Verify success message with shade ID
- [ ] Type `list shades` to confirm shade was added
- [ ] Expected: Shade 1 created with name "Test Shade"

#### Test 2.2: Shade Information
- [ ] Type `info 1` and press Enter
- [ ] Verify shade details are displayed
- [ ] Check that shade shows as "not paired"
- [ ] Expected: Complete shade information shown

#### Test 2.3: Set Shade Properties
- [ ] Type `set 1 name "Living Room Blind"`
- [ ] Type `set 1 uptime 12000`
- [ ] Type `set 1 downtime 15000`
- [ ] Type `info 1` to verify changes
- [ ] Expected: Properties updated successfully

### 3. Remote Discovery Tests

#### Test 3.1: Listen Mode
- [ ] Type `listen` and press Enter
- [ ] Press buttons on Somfy remote
- [ ] Verify remote commands are displayed
- [ ] Press any key to exit listen mode
- [ ] Expected: Remote activity shown with address, rolling code, command

#### Test 3.2: Scan Mode
- [ ] Type `scan` and press Enter
- [ ] Press buttons on unknown Somfy remote
- [ ] Verify "NEW REMOTE DISCOVERED" message
- [ ] Press any key to exit scan mode
- [ ] Expected: Unknown remotes highlighted and tracked

### 4. Pairing Tests

#### Test 4.1: Shade Pairing
- [ ] Type `pair 1` and press Enter
- [ ] Follow on-screen instructions
- [ ] Press and hold PROG button on Somfy remote for 3 seconds
- [ ] Verify shade jogs (if connected to actual shade)
- [ ] Verify success message with remote address
- [ ] Expected: Shade paired successfully

#### Test 4.2: Verify Pairing
- [ ] Type `info 1` and press Enter
- [ ] Verify "Paired: Yes" is shown
- [ ] Verify remote address is displayed
- [ ] Verify rolling code is set
- [ ] Expected: Pairing information correctly stored

#### Test 4.3: Test Communication
- [ ] Type `test 1` and press Enter
- [ ] Verify test command is sent
- [ ] Check if shade responds (if connected)
- [ ] Expected: MY command sent, shade should jog

### 5. Shade Control Tests

#### Test 5.1: Basic Movement Commands
- [ ] Type `1 up` and press Enter
- [ ] Verify "Moving shade 1 UP" message
- [ ] Type `1 down` and press Enter
- [ ] Verify "Moving shade 1 DOWN" message
- [ ] Type `1 my` and press Enter
- [ ] Verify "Moving shade 1 to MY position" message
- [ ] Expected: Commands sent successfully

#### Test 5.2: Position Control
- [ ] Type `1 pos 50` and press Enter
- [ ] Verify position command message
- [ ] Type `1 pos 0` and press Enter
- [ ] Type `1 pos 100` and press Enter
- [ ] Expected: Position commands accepted

#### Test 5.3: MY Position Setting
- [ ] Type `1 setmy` and press Enter
- [ ] Verify MY position set message
- [ ] Type `info 1` to check MY position
- [ ] Expected: MY position updated

### 6. Error Handling Tests

#### Test 6.1: Invalid Commands
- [ ] Type `invalid_command` and press Enter
- [ ] Verify error message
- [ ] Type `1 invalid_shade_command` and press Enter
- [ ] Verify error message
- [ ] Expected: Clear error messages for invalid input

#### Test 6.2: Invalid Shade IDs
- [ ] Type `info 99` and press Enter
- [ ] Type `pair 99` and press Enter
- [ ] Type `99 up` and press Enter
- [ ] Expected: "Invalid shade ID" or "Shade not found" messages

#### Test 6.3: Unpaired Shade Commands
- [ ] Create unpaired shade: `add shade "Unpaired"`
- [ ] Type `test 2` (assuming shade 2 is unpaired)
- [ ] Verify appropriate error message
- [ ] Expected: Message indicating shade needs pairing

### 7. Advanced Features Tests

#### Test 7.1: Multiple Shades
- [ ] Add second shade: `add shade "Shade 2"`
- [ ] Pair second shade to different remote
- [ ] Test controlling both shades independently
- [ ] Expected: Both shades work independently

#### Test 7.2: Unpair Functionality
- [ ] Type `unpair 1` and press Enter
- [ ] Verify unpair success message
- [ ] Type `info 1` to confirm unpaired status
- [ ] Expected: Shade successfully unpaired

#### Test 7.3: Delete Shade
- [ ] Type `delete 2` and press Enter
- [ ] Verify deletion message
- [ ] Type `list shades` to confirm removal
- [ ] Expected: Shade removed from list

### 8. Stress Tests

#### Test 8.1: Rapid Commands
- [ ] Send multiple commands quickly
- [ ] Verify system remains responsive
- [ ] Check for any errors or crashes
- [ ] Expected: System handles rapid input gracefully

#### Test 8.2: Long Operation
- [ ] Leave system running for extended period
- [ ] Periodically send commands
- [ ] Monitor for memory leaks or crashes
- [ ] Expected: Stable long-term operation

## Test Results Template

### Test Environment
- Date: ___________
- Firmware Version: ___________
- Hardware: ___________
- Tester: ___________

### Results Summary
- Total Tests: ___________
- Passed: ___________
- Failed: ___________
- Skipped: ___________

### Failed Tests
| Test ID | Description | Expected | Actual | Notes |
|---------|-------------|----------|--------|-------|
|         |             |          |        |       |

### Recommendations
- [ ] Ready for production use
- [ ] Requires minor fixes
- [ ] Requires major fixes
- [ ] Not ready for release

### Notes
_Additional observations and comments_
