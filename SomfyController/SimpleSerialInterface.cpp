#include "SimpleSerialInterface.h"

// Global instance
SimpleSerialInterface serialInterface;

SimpleSerialInterface::SimpleSerialInterface() {
  commandReady = false;
  lastHeartbeat = 0;
}

void SimpleSerialInterface::begin(unsigned long baudRate) {
  Serial.begin(baudRate);
  delay(1000);  // Allow serial to initialize
  printWelcome();
}

void SimpleSerialInterface::loop() {
  // Read serial input
  while (Serial.available()) {
    char c = Serial.read();
    
    if (c == '\n' || c == '\r') {
      if (inputBuffer.length() > 0) {
        commandReady = true;
      }
    } else if (c == '\b' || c == 127) { // Backspace
      if (inputBuffer.length() > 0) {
        inputBuffer.remove(inputBuffer.length() - 1);
        Serial.print("\b \b");
      }
    } else if (c >= 32 && c <= 126) { // Printable characters
      inputBuffer += c;
      Serial.print(c);
    }
  }
  
  // Process command if ready
  if (commandReady) {
    Serial.println(); // New line
    
    String command = inputBuffer;
    command.trim();
    inputBuffer = "";
    commandReady = false;
    
    if (command.length() > 0) {
      processCommand(command);
    }
    
    Serial.print("> ");
  }
  
  // Send periodic heartbeat
  if (millis() - lastHeartbeat > 30000) { // Every 30 seconds
    sendHeartbeat();
    lastHeartbeat = millis();
  }
}

void SimpleSerialInterface::processCommand(const String& input) {
  Command cmd = parseCommand(input);
  
  if (!cmd.valid) {
    sendError("Invalid command format. Type 'help' for usage.");
    return;
  }
  
  // Execute command
  if (cmd.action == "help") {
    handleHelpCommand();
  } else if (cmd.action == "list") {
    handleListCommand();
  } else if (cmd.action == "status") {
    handleStatusCommand();
  } else if (cmd.action == "system") {
    handleSystemCommand();
  } else if (cmd.action == "move") {
    handleMoveCommand(cmd);
  } else if (cmd.action == "stop") {
    handleStopCommand(cmd);
  } else if (cmd.action == "add") {
    handleAddCommand(cmd);
  } else if (cmd.action == "remove") {
    handleRemoveCommand(cmd);
  } else if (cmd.action == "setname") {
    handleSetNameCommand(cmd);
  } else if (cmd.action == "setmy") {
    handleSetMyCommand(cmd);
  } else if (cmd.action == "reset") {
    handleResetCommand();
  } else {
    sendError("Unknown command: " + cmd.action);
  }
}

Command SimpleSerialInterface::parseCommand(const String& input) {
  // Try JSON format first
  Command cmd = parseJsonCommand(input);
  if (cmd.valid) return cmd;
  
  // Try text format
  return parseTextCommand(input);
}

Command SimpleSerialInterface::parseTextCommand(const String& input) {
  Command cmd;
  
  // Split input into tokens
  int spaceIndex = input.indexOf(' ');
  if (spaceIndex == -1) {
    cmd.action = input;
    cmd.action.toLowerCase();
    cmd.valid = (cmd.action == "help" || cmd.action == "list" || 
                 cmd.action == "status" || cmd.action == "system" || 
                 cmd.action == "reset");
    return cmd;
  }
  
  cmd.action = input.substring(0, spaceIndex);
  cmd.action.toLowerCase();
  String params = input.substring(spaceIndex + 1);
  params.trim();
  
  // Parse parameters based on command
  if (cmd.action == "move" || cmd.action == "stop" || cmd.action == "status" || cmd.action == "setmy") {
    // Format: move <shadeId> [position]
    int nextSpace = params.indexOf(' ');
    if (nextSpace == -1) {
      cmd.shadeId = params.toInt();
    } else {
      cmd.shadeId = params.substring(0, nextSpace).toInt();
      cmd.position = params.substring(nextSpace + 1).toFloat();
    }
    cmd.valid = (cmd.shadeId < MAX_SHADES);
  } else if (cmd.action == "add") {
    // Format: add <address> [name]
    int nextSpace = params.indexOf(' ');
    if (nextSpace == -1) {
      cmd.address = strtoul(params.c_str(), nullptr, 16);
    } else {
      cmd.address = strtoul(params.substring(0, nextSpace).c_str(), nullptr, 16);
      cmd.name = params.substring(nextSpace + 1);
    }
    cmd.valid = (cmd.address != 0);
  } else if (cmd.action == "remove") {
    // Format: remove <shadeId>
    cmd.shadeId = params.toInt();
    cmd.valid = (cmd.shadeId < MAX_SHADES);
  } else if (cmd.action == "setname") {
    // Format: setname <shadeId> <name>
    int nextSpace = params.indexOf(' ');
    if (nextSpace != -1) {
      cmd.shadeId = params.substring(0, nextSpace).toInt();
      cmd.name = params.substring(nextSpace + 1);
      cmd.valid = (cmd.shadeId < MAX_SHADES && cmd.name.length() > 0);
    }
  }
  
  return cmd;
}

Command SimpleSerialInterface::parseJsonCommand(const String& input) {
  Command cmd;
  
  DynamicJsonDocument doc(1024);
  DeserializationError error = deserializeJson(doc, input);
  
  if (error) {
    return cmd; // Invalid JSON
  }
  
  if (doc.containsKey("action")) {
    cmd.action = doc["action"].as<String>();
    cmd.action.toLowerCase();
  } else {
    return cmd; // No action specified
  }
  
  if (doc.containsKey("shadeId")) {
    cmd.shadeId = doc["shadeId"];
  }
  
  if (doc.containsKey("position")) {
    cmd.position = doc["position"];
  }
  
  if (doc.containsKey("address")) {
    cmd.address = doc["address"];
  }
  
  if (doc.containsKey("name")) {
    cmd.name = doc["name"].as<String>();
  }
  
  cmd.valid = true;
  return cmd;
}

void SimpleSerialInterface::sendResponse(const Response& response) {
  DynamicJsonDocument doc(2048);
  
  doc["type"] = response.type;
  doc["success"] = response.success;
  doc["timestamp"] = millis();
  
  if (response.message.length() > 0) {
    doc["message"] = response.message;
  }
  
  if (!response.data.isNull()) {
    doc["data"] = response.data;
  }
  
  serializeJson(doc, Serial);
  Serial.println();
}

void SimpleSerialInterface::sendError(const String& message) {
  Response response;
  response.type = "error";
  response.success = false;
  response.message = message;
  sendResponse(response);
}

void SimpleSerialInterface::sendSuccess(const String& message, JsonDocument* data) {
  Response response;
  response.type = "success";
  response.success = true;
  response.message = message;
  if (data) {
    response.data = *data;
  }
  sendResponse(response);
}

void SimpleSerialInterface::sendJson(JsonDocument& doc) {
  serializeJson(doc, Serial);
  Serial.println();
}

void SimpleSerialInterface::printWelcome() {
  Serial.println();
  Serial.println("=== Simple Somfy RTS Controller ===");
  Serial.println("Version 1.0 - Serial Interface");
  Serial.println("Type 'help' for available commands");
  Serial.println();
  Serial.print("> ");
}

void SimpleSerialInterface::printHelp() {
  Serial.println("Available Commands:");
  Serial.println();
  Serial.println("System Commands:");
  Serial.println("  help                    - Show this help");
  Serial.println("  system                  - Show system status");
  Serial.println("  list                    - List all shades");
  Serial.println("  reset                   - Factory reset (removes all shades)");
  Serial.println();
  Serial.println("Shade Commands:");
  Serial.println("  add <addr> [name]       - Add shade (addr in hex)");
  Serial.println("  remove <id>             - Remove shade");
  Serial.println("  move <id> <pos>         - Move shade to position (0-100)");
  Serial.println("  stop <id>               - Stop shade movement");
  Serial.println("  status <id>             - Get shade status");
  Serial.println("  setname <id> <name>     - Set shade name");
  Serial.println("  setmy <id>              - Set MY position (for programming)");
  Serial.println();
  Serial.println("JSON Format:");
  Serial.println("  {\"action\":\"move\",\"shadeId\":0,\"position\":50}");
  Serial.println("  {\"action\":\"add\",\"address\":1179649,\"name\":\"Living Room\"}");
  Serial.println();
  Serial.println("Examples:");
  Serial.println("  add 120001 \"Living Room\"");
  Serial.println("  move 0 75");
  Serial.println("  stop 0");
}

// Command handlers
void SimpleSerialInterface::handleListCommand() {
  DynamicJsonDocument doc(2048);
  doc["type"] = "shadeList";
  doc["success"] = true;
  doc["timestamp"] = millis();

  somfyController.listShades(doc);
  sendJson(doc);
}

void SimpleSerialInterface::handleStatusCommand() {
  DynamicJsonDocument doc(1024);
  doc["type"] = "shadeStatus";
  doc["success"] = true;
  doc["timestamp"] = millis();

  // Get status for all shades
  JsonArray shades = doc.createNestedArray("shades");
  for (uint8_t i = 0; i < MAX_SHADES; i++) {
    SomfyShade* shade = somfyController.getShade(i);
    if (shade) {
      JsonObject shadeObj = shades.createNestedObject();
      shadeObj["id"] = shade->getId();
      shadeObj["name"] = shade->getName();
      shadeObj["position"] = shade->getCurrentPosition();
      shadeObj["target"] = shade->getTargetPosition();
      shadeObj["direction"] = static_cast<int8_t>(shade->getDirection());
      shadeObj["moving"] = shade->isMoving();
    }
  }

  sendJson(doc);
}

void SimpleSerialInterface::handleSystemCommand() {
  DynamicJsonDocument doc(1024);
  doc["type"] = "systemStatus";
  doc["success"] = true;
  doc["timestamp"] = millis();

  somfyController.getSystemStatus(doc);
  sendJson(doc);
}

void SimpleSerialInterface::handleMoveCommand(const Command& cmd) {
  if (cmd.position < 0 || cmd.position > 100) {
    sendError("Position must be between 0 and 100");
    return;
  }

  if (somfyController.moveShade(cmd.shadeId, cmd.position)) {
    sendSuccess("Moving shade " + String(cmd.shadeId) + " to position " + String(cmd.position));
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleStopCommand(const Command& cmd) {
  if (somfyController.stopShade(cmd.shadeId)) {
    sendSuccess("Stopped shade " + String(cmd.shadeId));
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleAddCommand(const Command& cmd) {
  SomfyShade* shade = somfyController.addShade(cmd.address, cmd.name.c_str());
  if (shade) {
    somfyController.saveConfiguration();
    sendSuccess("Added shade " + String(shade->getId()) + " with address 0x" + String(cmd.address, HEX));
    notifyShadeAdded(shade->getId());
  } else {
    sendError("Failed to add shade - maximum number reached or invalid address");
  }
}

void SimpleSerialInterface::handleRemoveCommand(const Command& cmd) {
  if (somfyController.removeShade(cmd.shadeId)) {
    somfyController.saveConfiguration();
    sendSuccess("Removed shade " + String(cmd.shadeId));
    notifyShadeRemoved(cmd.shadeId);
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleSetNameCommand(const Command& cmd) {
  SomfyShade* shade = somfyController.getShade(cmd.shadeId);
  if (!shade) {
    sendError("Shade " + String(cmd.shadeId) + " not found");
    return;
  }

  shade->setName(cmd.name.c_str());
  shade->save();
  sendSuccess("Set name for shade " + String(cmd.shadeId) + " to: " + cmd.name);
}

void SimpleSerialInterface::handleSetMyCommand(const Command& cmd) {
  if (somfyController.setShadeMyPosition(cmd.shadeId)) {
    sendSuccess("Sent MY command to shade " + String(cmd.shadeId));
  } else {
    sendError("Shade " + String(cmd.shadeId) + " not found");
  }
}

void SimpleSerialInterface::handleHelpCommand() {
  printHelp();
}

void SimpleSerialInterface::handleResetCommand() {
  somfyController.factoryReset();
  sendSuccess("Factory reset completed - all shades removed");
}

// Event notifications
void SimpleSerialInterface::notifyShadeStateChanged(uint8_t shadeId) {
  SomfyShade* shade = somfyController.getShade(shadeId);
  if (!shade) return;

  DynamicJsonDocument doc(512);
  doc["type"] = "stateChanged";
  doc["success"] = true;
  doc["timestamp"] = millis();
  doc["shadeId"] = shadeId;
  doc["position"] = shade->getCurrentPosition();
  doc["target"] = shade->getTargetPosition();
  doc["direction"] = static_cast<int8_t>(shade->getDirection());
  doc["moving"] = shade->isMoving();

  sendJson(doc);
}

void SimpleSerialInterface::notifyShadeAdded(uint8_t shadeId) {
  SomfyShade* shade = somfyController.getShade(shadeId);
  if (!shade) return;

  DynamicJsonDocument doc(512);
  doc["type"] = "shadeAdded";
  doc["success"] = true;
  doc["timestamp"] = millis();
  doc["shadeId"] = shadeId;
  doc["name"] = shade->getName();
  doc["address"] = shade->getAddress();

  sendJson(doc);
}

void SimpleSerialInterface::notifyShadeRemoved(uint8_t shadeId) {
  DynamicJsonDocument doc(256);
  doc["type"] = "shadeRemoved";
  doc["success"] = true;
  doc["timestamp"] = millis();
  doc["shadeId"] = shadeId;

  sendJson(doc);
}

void SimpleSerialInterface::sendHeartbeat() {
  DynamicJsonDocument doc(256);
  doc["type"] = "heartbeat";
  doc["success"] = true;
  doc["timestamp"] = millis();
  doc["uptime"] = millis();
  doc["freeHeap"] = ESP.getFreeHeap();

  sendJson(doc);
}
